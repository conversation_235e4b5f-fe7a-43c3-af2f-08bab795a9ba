# Paimon 表对比 SQL 使用说明

## 文件说明

0. **00_Catalog设置.sql** - 创建两个不同版本的 Paimon Catalog（必须先执行）
1. **01_差异统计.sql** - 统计有差异的记录总数和涉及的 VIN 数量
2. **02_简化对比.sql** - 显示主键和差异字段数量的简化对比
3. **03_详细字段对比.sql** - 显示具体字段差异内容的详细对比
4. **04_字段对比_*.sql** - 特定字段的详细对比

## 使用步骤

### 1. 设置 Catalog
**重要：必须先执行 `00_Catalog设置.sql` 创建两个不同版本的 Catalog**

### 2. 执行顺序
建议按以下顺序执行：

0. **首先运行** `00_Catalog设置.sql` 创建 Catalog 和 Database
1. 运行 `01_差异统计.sql` 了解差异记录的总体情况
2. 运行 `02_简化对比.sql` 查看差异概况和主键信息
3. 运行 `03_详细字段对比.sql` 查看具体的字段差异
4. 根据需要运行特定字段的对比 SQL

### 3. 注意事项

- **两个 Catalog 配置**：
  - `paimon_catalog_v06`: 指向 0.6 版本的 warehouse (tos://hs-bdp-private-prod/user/paimon/warehouse)
  - `paimon_catalog_v08`: 指向 0.8 版本的 warehouse (tos://hs-bdp-private-lakehouse-prod/user/paimon/warehouse)
- 表名在两个 Catalog 中完全相同：`dwd_prd_ddc_data_periodcanlin_p12l_format_s_di`
- 主键字段假设为：vin, ct, st, at, uuid
- 由于字段数量很多(1880个)，详细对比只显示前50个字段的差异
- 所有 SQL 都限制返回 1000 条记录，可根据需要调整

### 4. 结果解读

- **差异统计**：显示总的差异记录数和涉及的车辆数
- **简化对比**：每行显示一个有差异的记录及其差异字段数量
- **详细对比**：显示具体哪些字段有差异以及差异的值
- **字段对比**：针对特定字段的详细对比，包括 NULL 值处理

### 5. 性能优化建议

- 如果数据量很大，建议先在小范围数据上测试
- 可以添加时间范围过滤条件来减少数据量
- 考虑在主键字段上建立索引以提高查询性能

## 表结构信息

- **0.6 版本 Catalog**: paimon_catalog_v06
- **0.8 版本 Catalog**: paimon_catalog_v08
- **Database**: imedw
- **表名**: dwd_prd_ddc_data_periodcanlin_p12l_format_s_di
- **字段总数**: 1880个
- **主键字段**: vin, ct, st, at, uuid
