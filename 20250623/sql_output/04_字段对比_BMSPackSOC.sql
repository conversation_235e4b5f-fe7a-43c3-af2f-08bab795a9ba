-- 特定字段 'BMSPackSOC' 对比 SQL - 输出到 Print Sink

-- 1. 创建 Print Sink 表
CREATE TABLE print_field_comparison_BMSPackSOC (
    `vin` STRING
    `ct` STRING
    `st` STRING
    `at` STRING
    `uuid` STRING,
    v06_BMSPackSOC STRING,
    v08_BMSPackSOC STRING,
    comparison_result STRING,
    comparison_time TIMESTAMP(3)
) WITH (
    'connector' = 'print',
    'print-identifier' = 'paimon-field-BMSPackSOC-comparison'
);

-- 2. 插入字段对比结果到 Print Sink
INSERT INTO print_field_comparison_BMSPackSOC
SELECT
    t1.`vin`
    t1.`ct`
    t1.`st`
    t1.`at`
    t1.`uuid`,
    CAST(t1.`BMSPackSOC` AS STRING) as v06_BMSPackSOC,
    CAST(t2.`BMSPackSOC` AS STRING) as v08_BMSPackSOC,
    CASE
        WHEN t1.`BMS<PERSON>ackSOC` IS NULL AND t2.`BMSPackSOC` IS NOT NULL THEN 'v0.6_NULL_v0.8_NOT_NULL'
        WHEN t1.`BMSPackSOC` IS NOT NULL AND t2.`BMSPackSOC` IS NULL THEN 'v0.6_NOT_NULL_v0.8_NULL'
        WHEN t1.`BMSPackSOC` != t2.`BMSPackSOC` THEN 'VALUES_DIFFERENT'
        ELSE 'SAME'
    END as comparison_result,
    CURRENT_TIMESTAMP as comparison_time
FROM `paimon_catalog_v06`.`imedw`.`dwd_prd_ddc_data_periodcanlin_p12l_format_s_di` t1
INNER JOIN `paimon_catalog_v08`.`imedw`.`dwd_prd_ddc_data_periodcanlin_p12l_format_s_di` t2
ON t1.`vin` = t2.`vin` AND t1.`ct` = t2.`ct` AND t1.`st` = t2.`st` AND t1.`at` = t2.`at` AND t1.`uuid` = t2.`uuid`
WHERE (t1.`BMSPackSOC` IS NULL AND t2.`BMSPackSOC` IS NOT NULL) OR
      (t1.`BMSPackSOC` IS NOT NULL AND t2.`BMSPackSOC` IS NULL) OR
      (t1.`BMSPackSOC` IS NOT NULL AND t2.`BMSPackSOC` IS NOT NULL AND t1.`BMSPackSOC` != t2.`BMSPackSOC`)
LIMIT 1000;