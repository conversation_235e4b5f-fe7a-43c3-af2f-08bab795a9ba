-- Paimon Catalog 设置 SQL
-- 创建两个不同版本的 Paimon Catalog

-- 1. 创建 0.6 版本的 Catalog
CREATE CATALOG paimon_catalog_v06
WITH
  (
    'type' = 'paimon',
    'warehouse' = 'tos://hs-bdp-private-prod/user/paimon/warehouse',
    'fs.s3a.connection.maximum' = '1000'
  );

-- 2. 创建 0.6 版本的 Database
CREATE DATABASE IF NOT EXISTS paimon_catalog_v06.imedw;

-- 3. 创建 0.8 版本的 Catalog
CREATE CATALOG paimon_catalog_v08
WITH
  (
    'type' = 'paimon',
    'warehouse' = 'tos://hs-bdp-private-lakehouse-prod/user/paimon/warehouse',
    'fs.s3a.connection.maximum' = '1000'
  );

-- 4. 创建 0.8 版本的 Database
CREATE DATABASE IF NOT EXISTS paimon_catalog_v08.imedw;

-- 注意：执行对比 SQL 之前，请先执行上述 Catalog 创建语句
