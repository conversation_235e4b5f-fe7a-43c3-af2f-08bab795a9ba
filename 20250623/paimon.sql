

CREATE CATALOG paimon_catalog
WITH
  (
    'type' = 'paimon',
    'warehouse' = 'tos://hs-bdp-private-lakehouse-prod/user/paimon/warehouse',
    'fs.s3a.connection.maximum' = '1000'
  );


CREATE DATABASE  IF NOT EXISTS paimon_catalog.imedw;

-- drop table `paimon_catalog`.`imedw`.`dwd_prd_ddc_data_periodcanlin_p12l_format_s_di`;
CREATE TABLE
  IF NOT EXISTS `paimon_catalog`.`imedw`.`dwd_prd_ddc_data_periodcanlin_p12l_format_s_di` (
    `vin` STRING,
    `ct` BIGINT,
    `st` BIGINT,
    `at` BIGINT,
    `uuid` STRING,
    `col` BIGINT,
    `rct` BIGINT,
    `r` BIGINT,
    `ABSA` STRING,
    `ABSF` STRING,
    `ABSIO` STRING,
    `ACAmbtTem` STRING,
    `ACAmbtTemV` STRING,
    `ACAutoDspCmd` STRING,
    `ACCAccReqSts_CH` STRING,
    `ACCAccReqSts_PT` STRING,
    `ACCAccReqVal` STRING,
    `AccelActuPos` STRING,
    `AccelActuPosV` STRING,
    `AccelerationXsecinfo` STRING,
    `AccelerationYsecinfo` STRING,
    `AccelerationZsecinfo` STRING,
    `AccelOvrd` STRING,
    `AcceZ` STRING,
    `ACCIntervQuit_Rddy` STRING,
    `ACComprActuPwr` STRING,
    `ACComprMngmntInfo` STRING,
    `ACCoolngFanPWMReq` STRING,
    `ACCOtptShaftTotToqReqSts` STRING,
    `ACCOtptShaftTotToqReqVal` STRING,
    `ACCSysFltSts` STRING,
    `ACCSysFltSts_PT` STRING,
    `ACCSysSts` STRING,
    `ACCSysSts_PT` STRING,
    `ACEcoDspCmd` STRING,
    `ACEvapoTem` STRING,
    `ACFrtBlwrSpdDspCmd` STRING,
    `ACFrtBlwrWorkPcnt` STRING,
    `ACHepaDspCmd` STRING,
    `ACHiSideFludPrs` STRING,
    `ACHtrCoreTem` STRING,
    `ACHtrPwr` STRING,
    `ACInCarTem` STRING,
    `ACLAirMdDspCmd` STRING,
    `ACLoSideFludPrs` STRING,
    `ACLTemDspCmd` STRING,
    `ACOnOffDspCmd` STRING,
    `ACPwr` STRING,
    `ACRcctnDspMd` STRING,
    `ACRcctnPcnt` STRING,
    `ACRrBlwrLvlDspCmd` STRING,
    `ACRrTemDspCmd` STRING,
    `ACRTemDspCmd` STRING,
    `ActualDampingCurr_FL` STRING,
    `ActualDampingCurr_FR` STRING,
    `ActualDampingCurr_RL` STRING,
    `ActualDampingCurr_RR` STRING,
    `ActuPnonAng` STRING,
    `ActuRoadWhlAng` STRING,
    `ActuRoadWhlAngRddy` STRING,
    `ACWindTem` STRING,
    `AEBActv` STRING,
    `AEBDclReqSts` STRING,
    `AEBDclReqVal` STRING,
    `AEBDspCmd` STRING,
    `AEBMsgReq` STRING,
    `AEBPrflReq` STRING,
    `AEBSysFltSts` STRING,
    `AEBSysSts` STRING,
    `AEBTrgtRcgntn` STRING,
    `AGS1Pos` STRING,
    `AGS2Pos` STRING,
    `AGSFaultInfo` STRING,
    `AIParkngDsp` STRING,
    `AIParkngSts` STRING,
    `AIPilotCndNotSt` STRING,
    `AIPilotDrvrSeldTrgtDistLvl` STRING,
    `AIPilotDrvrSelTrgtSpd` STRING,
    `AIPiloTIntervQuit` STRING,
    `AIPilotMsgIndcr` STRING,
    `AIPilotSwSts` STRING,
    `AIPilotSysFltSts` STRING,
    `AIPilotSysSts` STRING,
    `AIPilotWrnng` STRING,
    `AIPilotWrnng_RollingCounter` STRING,
    `AIPkgAPASCSAcclReqVal` STRING,
    `AIPkgEPSRdWhlAngReqSts` STRING,
    `AIPkgEPSRdWhlAngReqStsV` STRING,
    `AIPkgEPSReqRdWhlAng` STRING,
    `AIPltEPSRdWhlAngReqSts` STRING,
    `AIPltEPSRdWhlAngReqStsRddy` STRING,
    `AIPltEPSRdWhlAngReqStsV` STRING,
    `AIPltEPSRdWhlAngReqStsVRddy` STRING,
    `AIPltEPSReqRdWhlAng` STRING,
    `AIPltEPSReqRdWhlAngRddy` STRING,
    `AIPltEPSReqRdWhlAngV` STRING,
    `AIPltEPSReqRdWhlAngVRddy` STRING,
    `AirbagDpl` STRING,
    `AirbagDplInvsn` STRING,
    `AirbagSysFlt` STRING,
    `AirbagSysFltIndCmd` STRING,
    `AirCtrlrHVILClsd` STRING,
    `AirCtrlrHVILClsdV` STRING,
    `AirvntRZoneShtdwnReqDspCmd` STRING,
    `AllMuteFdbk` STRING,
    `AllMuteReq` STRING,
    `AltitudeINSsecinfo` STRING,
    `AltitudeRTK` STRING,
    `AmbtTemSnsrFlrSts` STRING,
    `AMP_NKI_INFOCAN` STRING,
    `AMPAvlbly` STRING,
    `AMPCanVoltSts` STRING,
    `AMPClsAbNormalSts` STRING,
    `AMPClsDNormalSts` STRING,
    `AMPDiaVoltSts` STRING,
    `AMPDspInitSts` STRING,
    `AMPDspResetAgainSts` STRING,
    `AMPDspResetSts` STRING,
    `AMPDspSpiBusState` STRING,
    `AMPDspSts` STRING,
    `AMPEqmode` STRING,
    `AMPFucVoltSts` STRING,
    `AMPHWVersionSts` STRING,
    `AMPMcuSpiBusRecSts` STRING,
    `AMPMcuSpiBusSentSts` STRING,
    `AMPMcuSysSts` STRING,
    `AMPPwrRdySts` STRING,
    `AMPStartupSts` STRING,
    `AMPTurnOnRdyStsFdbk` STRING,
    `AMR_NKI_BDCAN` STRING,
    `AMRSts` STRING,
    `AMRSysSts` STRING,
    `AngularRateYsecinfo` STRING,
    `APASCSAcclReqSts` STRING,
    `APASCSAcclReqVal` STRING,
    `APAShifterPosReqd` STRING,
    `APCA` STRING,
    `apca_height` STRING,
    `ATC_NKI_BDCAN` STRING,
    `ATCAvlbly` STRING,
    `AutocParkngSwIndrCmd_l` STRING,
    `AutocParkngSwReq` STRING,
    `AutocParkngSwReq_l` STRING,
    `AutodDrvngCndNotSt` STRING,
    `AutodDrvngRestDist` STRING,
    `AutodDrvngSysMdReq` STRING,
    `AutodDrvngSysMdReqResp` STRING,
    `AutodDrvngSysMdReqRespV` STRING,
    `AutodDrvngSysSCSDrvOffReq` STRING,
    `AutodDrvngSysSCSSdslReq` STRING,
    `AutodDrvngSysSts_PT` STRING,
    `AutodDrvngToqReqResp` STRING,
    `AutoHoldMsg` STRING,
    `AutoHoldSysSts` STRING,
    `AutoLghtOnReq` STRING,
    `AutoMainBeamLghtOn` STRING,
    `AutoMainBeamLghtReq` STRING,
    `AutoWlcmLghtA` STRING,
    `BatAgngSta` STRING,
    `BatCrnt` STRING,
    `BatCrnt_R` STRING,
    `BatIncnstncy` STRING,
    `BatSOC` STRING,
    `BatSOC_R` STRING,
    `BatSOCSts` STRING,
    `BatSOFVol1` STRING,
    `BattCoolMangmntInfo` STRING,
    `BatVol` STRING,
    `BatVol_R` STRING,
    `BatVolSts` STRING,
    `BCM_BD_NKI` STRING,
    `BCM_BD_NOI` STRING,
    `BCM_BD_NWI` STRING,
    `BCM_BDEx_NKI` STRING,
    `BCM_BDEx_NOI` STRING,
    `BCM_BDEx_NWI` STRING,
    `BCM_BodyExtdAvlbly` STRING,
    `BCM_NWI_BDCAN` STRING,
    `BCM_NWI_BDEXTDCAN` STRING,
    `BCMAvlbly` STRING,
    `BCMBTCtrlPotcl` STRING,
    `BCMCofignSts2` STRING,
    `BCMDrvrDetSnsrFltSts` STRING,
    `BCMDrvrDetSts` STRING,
    `BCMNoSmtKeyInVehRmndr` STRING,
    `BCMPutSmtKeyToBkupPosR` STRING,
    `BCMPwrMdHwdSta` STRING,
    `BCMRunCrkF` STRING,
    `BCMSyncSmtKeyRmndr` STRING,
    `BCMTakeSmtKeyOutOfSR` STRING,
    `BCMVehLckUnlckActn` STRING,
    `BleCmftLckSet` STRING,
    `BleCmftUnlckSet` STRING,
    `BlePEPSFnFlr` STRING,
    `BleScurReq` STRING,
    `BleScurResp` STRING,
    `BlwFaultInfo` STRING,
    `BMS_NKI_PTCANFD` STRING,
    `BMS_NKI_PTEXTDCAN` STRING,
    `BMS_NOI_PTCANFD` STRING,
    `BMS_NOI_PTEXTDCAN` STRING,
    `BMS_NWI_PTCANFD` STRING,
    `BMS_NWI_PTEXTDCAN` STRING,
    `BMSAltngChrgCrntDspCmd` STRING,
    `BMSAvlblEnrg` STRING,
    `BMSAvlblEnrgV` STRING,
    `BMSBalancingStatus` STRING,
    `BMSBatPrsAlrm` STRING,
    `BMSBatPrsAlrmBkup` STRING,
    `BMSBatPrsAlrmV` STRING,
    `BMSBatPrsAlrmVBkup` STRING,
    `BMSBatPrsFlt` STRING,
    `BMSBatPrsSnsrV` STRING,
    `BMSBatPrsSnsrVal` STRING,
    `BMSBatPrsSnsrValBkup` STRING,
    `BMSBatPrsSnsrVBkup` STRING,
    `BMSBatteryDataInfo14_DA08` STRING,
    `BMSBatteryDataInfo15_DA08` STRING,
    `BMSBscSta` STRING,
    `BMSBscStaBkup` STRING,
    `BMSBusbarTempMax` STRING,
    `BMSCellMaxTem` STRING,
    `BMSCellMaxTemIndx` STRING,
    `BMSCellMaxTemV` STRING,
    `BMSCellMaxVol` STRING,
    `BMSCellMaxVolIndx` STRING,
    `BMSCellMaxVolV` STRING,
    `BMSCellMinTem` STRING,
    `BMSCellMinVol` STRING,
    `BMSCellMinVolIndx` STRING,
    `BMSCellMinVolV` STRING,
    `BMSCellOverChrgdAlrm` STRING,
    `BMSCellTem[n]` STRING,
    `BMSCellVolSumNum` STRING,
    `BMSCellVoltFlt` STRING,
    `BMSChlrOffReq` STRING,
    `BMSChrgBuf` STRING,
    `BMSChrgCrntLmt` STRING,
    `BMSChrgCrntLmtV` STRING,
    `BMSChrgCtrlDspCmd` STRING,
    `BMSChrgCtrlResp` STRING,
    `BMSChrgOtptCrntReq` STRING,
    `BMSChrgOtptCrntReqV` STRING,
    `BMSChrgPeakPwr` STRING,
    `BMSChrgPwrLmt` STRING,
    `BMSChrgPwrLmtV` STRING,
    `BMSChrgrPlugCnctnIO` STRING,
    `BMSChrgSpRsn` STRING,
    `BMSChrgSts` STRING,
    `BMSChrgSts_l` STRING,
    `BMSChrgStsIO` STRING,
    `BMSChrgSttnMchngStaResp` STRING,
    `BMSChrgTrgtAvlblEnrg` STRING,
    `BMSChrgTrgtAvlblEnrgV` STRING,
    `BMSChrgTrgtSOCDspCmd` STRING,
    `BMSClntPumpPWMReq` STRING,
    `BMSClntPumpSts` STRING,
    `BMSClntTem` STRING,
    `BMSClntTemV` STRING,
    `BMSCMUFlt` STRING,
    `BMSCumuDschrgEnrg` STRING,
    `BMSCumuDschrgEnrgV` STRING,
    `BMSDircPstvRlyCtrlReq` STRING,
    `BMSDisTrgtAvlblEnrg` STRING,
    `BMSDisTrgtAvlblEnrgV` STRING,
    `BMSDschrgBuf` STRING,
    `BMSDschrgBufV` STRING,
    `BMSDschrgCrntLmt` STRING,
    `BMSDschrgCtnsPwrLmt` STRING,
    `BMSDsChrgCtrlResp` STRING,
    `BMSDschrgPwrLmt` STRING,
    `BMSDschrgPwrLmtBkup` STRING,
    `BMSDschrgPwrLmtV` STRING,
    `BMSDsChrgSpRsn` STRING,
    `BMSDsChrgTrgtSOCDspCmd` STRING,
    `BMSEmsnRltdMalfA` STRING,
    `BMSEnbPTC` STRING,
    `BMSEstdElecRng` STRING,
    `BMSFlameGasStatus` STRING,
    `BMSFltCt` STRING,
    `BMSFltLamp` STRING,
    `BMSFltLvl` STRING,
    `BMSFltLvlBkup` STRING,
    `BMSHighPrcsAvlblEnrg` STRING,
    `BMSHighPrcsAvlblEnrgV` STRING,
    `BMSHiSOCAlrm` STRING,
    `BMSHVILAlrm` STRING,
    `BMSHVILClsd` STRING,
    `BMSHVILClsdBkup` STRING,
    `BMSHVILSts` STRING,
    `BMSHVILStsBkup` STRING,
    `BMSHVReq` STRING,
    `BMSHVReqBkup` STRING,
    `BMSIndrtPstvRlyCtrlReq` STRING,
    `BMSInptClntTem` STRING,
    `BMSJumpngSOCAlrm` STRING,
    `BMSKeepSysAwkScene` STRING,
    `BMSLowPtIsltnRstcAlrm` STRING,
    `BMSLowSOCAlrm` STRING,
    `BMSLVBatInput` STRING,
    `BMSMainRelaySts` STRING,
    `BMSMainRelayStsBkup` STRING,
    `BMSMaxVolLmt` STRING,
    `BMSMaxVolLmtV` STRING,
    `BMSMinVolLmt` STRING,
    `BMSMinVolLmtV` STRING,
    `BMSNegativeBusVolt` STRING,
    `BMSNegPtIsltnRstc` STRING,
    `BMSNegPtIsltnRstcBkup` STRING,
    `BMSNegPtIsltnRstcV` STRING,
    `BMSNegPtIsltnRstcVBkup` STRING,
    `BMSNegRelayStatus` STRING,
    `BMSOfbdChrgRelaySts` STRING,
    `BMSOfbdChrgrPlugOn` STRING,
    `BMSOfbdChrgSpRsn` STRING,
    `BMSOfbdRlyCtrlReq` STRING,
    `BMSOffbdChargerWakeup` STRING,
    `BMSOnbdChargerWakeup` STRING,
    `BMSOnbdChrgngCmdReq` STRING,
    `BMSOnbdChrgrAltCrntLmt` STRING,
    `BMSOnbdChrgRelaySts` STRING,
    `BMSOnbdChrgrOtptCrntReq` STRING,
    `BMSOnbdChrgrOtptVolReq` STRING,
    `BMSOnbdChrgrPlugOn` STRING,
    `BMSOnbdChrgSpRsn` STRING,
    `BMSOverCellVolAlrm` STRING,
    `BMSOverPackVolAlrm` STRING,
    `BMSOverTemAlrm` STRING,
    `BMSPackCrnt` STRING,
    `BMSPackCrntBkup` STRING,
    `BMSPackCrntV` STRING,
    `BMSPackCrntVBkup` STRING,
    `BMSPackCurrent2` STRING,
    `BMSPackSOC` STRING,
    `BMSPackSOCActual` STRING,
    `BMSPackSOCBkup` STRING,
    `BMSPackSOCDsp` STRING,
    `BMSPackSOCDsp_l` STRING,
    `BMSPackSOCDspV` STRING,
    `BMSPackSOCDspV_l` STRING,
    `BMSPackSOCV` STRING,
    `BMSPackSOCVBkup` STRING,
    `BMSPackSOH` STRING,
    `BMSPackTemFlt` STRING,
    `BMSPackVol` STRING,
    `BMSPackVolBkup` STRING,
    `BMSPackVolMsmchAlrm` STRING,
    `BMSPackVoltFlt` STRING,
    `BMSPackVolV` STRING,
    `BMSPackVolVBkup` STRING,
    `BMSPoorCellCnstncyAlrm` STRING,
    `BMSPositiveBusVolt` STRING,
    `BMSPosPtIsltnRstc` STRING,
    `BMSPosPtIsltnRstcBkup` STRING,
    `BMSPosPtIsltnRstcV` STRING,
    `BMSPosPtIsltnRstcVBkup` STRING,
    `BMSPosRelayStatus` STRING,
    `BMSPreRelayStatus` STRING,
    `BMSPreThrmFltInd` STRING,
    `BMSPreThrmFltIndBkup` STRING,
    `BMSPTCHeatReqDspCmd` STRING,
    `BMSPTCHeatResp` STRING,
    `BMSPtIsltnRstc` STRING,
    `BMSPtIsltnRstcV` STRING,
    `BMSPumpPwrOnReq` STRING,
    `BMSPwrLmtChrctsPnt` STRING,
    `BMSRelayStep` STRING,
    `BMSReqPEBChrgVol` STRING,
    `BMSReqPTCPwr` STRING,
    `BMSReserChrgCtrlResp` STRING,
    `BMSReserCtrlDspCmd` STRING,
    `BMSReserStHourDspCmd` STRING,
    `BMSResetSts` STRING,
    `BMSStaOfHealthCapct` STRING,
    `BMSStwrVer` STRING,
    `BMSSysFltLamp` STRING,
    `BMSTemOverDifAlrm` STRING,
    `BMSUnderCellVolAlrm` STRING,
    `BMSUnderPackVolAlrm` STRING,
    `BMSVehicleWakeup` STRING,
    `BMSWriteEECmpd` STRING,
    `BMSWrlsChrgngCmd` STRING,
    `BMSWrlsChrgngOtptCrnt` STRING,
    `BMSWrlsChrgngOtptVol` STRING,
    `BMSWrlsChrgngSfty` STRING,
    `BMSWrlsChrgSpRsn` STRING,
    `BMSWrnngInfo` STRING,
    `BMSWrnngInfoBkup` STRING,
    `BMSWrnngInfoCRC` STRING,
    `BMSWrnngInfoPV` STRING,
    `BMSWrnngInfoRC` STRING,
    `BntOpenSts` STRING,
    `BPEPS_NKI_BDEXTDCAN` STRING,
    `BPEPS_NOI_BDEXTDCAN` STRING,
    `BPEPS_NWI_BDEXTDCAN` STRING,
    `BPEPSAvlbly` STRING,
    `BPEPSCtrlPotcl` STRING,
    `BrkFludLvlLow` STRING,
    `BrkFludLvlLowV` STRING,
    `BrkLghtOnReq` STRING,
    `BrkPdlAppd` STRING,
    `BrkPdlAppdV` STRING,
    `BrkPdlDrvrAppdPrs` STRING,
    `BrkPdlMdlSts` STRING,
    `BrkPdlPos` STRING,
    `BrkPdlPosV` STRING,
    `BrkPdlSimltdPrs` STRING,
    `BrkSysBrkLghtsReqd` STRING,
    `BrkSysHillStAstSts` STRING,
    `CARLog_NKI_INFOCAN` STRING,
    `CCSwStsDistDecSwA` STRING,
    `CCSwStsDistIncSwA` STRING,
    `CCSwStsSpdDecSwA` STRING,
    `CCSwStsSpdIncSwA` STRING,
    `CCU_NKI_PTCANFD` STRING,
    `CCU_NOI_PTCANFD` STRING,
    `CCU_NWI_PTCANFD` STRING,
    `CCUActiveWkup` STRING,
    `CCUChrgngStsIndrMd_l` STRING,
    `CCUDircPstvRelaySts` STRING,
    `CCUEleccLckCtrlDspCmd` STRING,
    `CCUIndrtPstvRelaySts` STRING,
    `CCUOfbdChrgCapVol` STRING,
    `CCUOfbdChrgRelaySts` STRING,
    `CCUOfbdChrgRelayStsV` STRING,
    `CCUOfbdChrgrSktVol` STRING,
    `CCUOfbdChrgrWkup` STRING,
    `CCUOfbdRelayWeldFlt` STRING,
    `CCUOfbdRelayWeldFltCt` STRING,
    `CCUOfbdRlyDiagResult` STRING,
    `CCUOfbdRlyNgtvVolt` STRING,
    `CCUOffBdChrgrPlugOn` STRING,
    `CCUOffBdChrgrPlugOnV` STRING,
    `CCUOnbdChrgrPlugOn` STRING,
    `CCUOnBdChrgrSktElecLckSta` STRING,
    `CCUOnBdChrgrSktElecLckStaV` STRING,
    `CCUOnbdChrgrSpRsn` STRING,
    `CCURawDTC` STRING,
    `CellBTem_Sec00` STRING,
    `CellFTem_Sec00` STRING,
    `cellSignalStrength` STRING,
    `ChACCAccReqResp` STRING,
    `ChAEBDclReqResp` STRING,
    `ChAEBPrflReqResp` STRING,
    `ChillerEXVActPosition` STRING,
    `ChllrInClntTemp` STRING,
    `ChmSndId` STRING,
    `ChmSndVolmStepReq` STRING,
    `ChrgngDoorPosSts` STRING,
    `ChrgngRmnngTime` STRING,
    `ClstrDspdVehSpd` STRING,
    `ClstrDspdVehSpd_l` STRING,
    `CMSPosRclReq` STRING,
    `CoDrvrDspCtrlReqICM` STRING,
    `CoDrvrDspCtrlSts_l` STRING,
    `ColMovSts_l` STRING,
    `ColPosCmdReq_l` STRING,
    `CompOutRefTem` STRING,
    `ComprOnOffDspCmd` STRING,
    `CoolingFanFaultInfo` STRING,
    `CoolngFanPWMFdbk` STRING,
    `CrntAvgElecCsump` STRING,
    `CrntAvgElecCsumpV` STRING,
    `CrntLoctnRoadCurve` STRING,
    `DayTimeRunningLghtF` STRING,
    `DCCurrentMaxHV` STRING,
    `DCDCVolStpt` STRING,
    `DCM_FL_NKI_BDCAN` STRING,
    `DCM_FL_NOI_BDCAN` STRING,
    `DCM_FL_NWI_BDCAN` STRING,
    `DCM_FR_NKI_BDCAN` STRING,
    `DCM_FR_NOI_BDCAN` STRING,
    `DCM_FR_NWI_BDCAN` STRING,
    `DCM_RL_NKI_BDCAN` STRING,
    `DCM_RL_NOI_BDCAN` STRING,
    `DCM_RL_NWI_BDCAN` STRING,
    `DCM_RR_NKI_BDCAN` STRING,
    `DCM_RR_NOI_BDCAN` STRING,
    `DCM_RR_NWI_BDCAN` STRING,
    `DCMFLPWLNotNormd` STRING,
    `DCMFLWndDclnSpc` STRING,
    `DCMFRWndDclnSpc` STRING,
    `DCOverCurrentHV` STRING,
    `DCOverCurrentLV` STRING,
    `DCOverVoltHV` STRING,
    `DCOverVoltLV` STRING,
    `DCUnderVoltHV` STRING,
    `DCUnderVoltLV` STRING,
    `DCVoltFail` STRING,
    `DfstOnDspCmd` STRING,
    `DigKey1Loctn` STRING,
    `DigKey2Loctn` STRING,
    `DigKeyExeAvlbl` STRING,
    `DigKeyExeAvlblV` STRING,
    `DipdBeamLghtOn` STRING,
    `DircnIndLampSwSts` STRING,
    `DircnIndLampSwStsMntry` STRING,
    `DLP_NarwSpcAstSts` STRING,
    `DLP_NKI_BDCAN` STRING,
    `DLP_RCWWrnng` STRING,
    `DLPBndngAstnEnbSts` STRING,
    `DLPCamrFltSts` STRING,
    `DLPLghtRdySts` STRING,
    `DoorLckCtrlReq` STRING,
    `DoorLckCtrlReq_FR_l` STRING,
    `DoorLckCtrlReq_l` STRING,
    `DrvngScurReq` STRING,
    `DrvngScurResp` STRING,
    `DrvrDoorOpenSts` STRING,
    `DrvrDoorOpenStsV` STRING,
    `DrvrDspCtrlReqICM` STRING,
    `DrvrDspCtrlSts_l` STRING,
    `DrvrPosRclReq` STRING,
    `DrvrPosRclSts` STRING,
    `DrvrPosSaveReq` STRING,
    `DrvrPosSpRclReq` STRING,
    `DrvrPrsc` STRING,
    `DrvrPWLInitnRmndr` STRING,
    `DrvrReqOtptRodTrvl` STRING,
    `DrvrSbltAtc` STRING,
    `DrvrSbltAtcCRC` STRING,
    `DrvrSbltAtcRC` STRING,
    `DrvrSbltAtcV` STRING,
    `DrvrSeatCtrlSts` STRING,
    `DrvrSeatEasyEntASta` STRING,
    `DrvrSeatEasyEntInhReq` STRING,
    `DrvrSeatEasyEntInhSts` STRING,
    `DrvrSeatHoztPosRclCmd` STRING,
    `DrvrSeatHoztSwReq` STRING,
    `DrvrSeatHoztSwReq_SW_l` STRING,
    `DrvrSeatHoztSwReq_UI` STRING,
    `DrvrSeatHoztSwReqV` STRING,
    `DrvrSeatMmryCtrlPosRclSts` STRING,
    `DrvrSeatPosGroupMmrySts` STRING,
    `DrvrSeatPosGroupRclReq` STRING,
    `DrvrSeatPosGroupRclSts` STRING,
    `DrvrSeatPosGroupSaveReq` STRING,
    `DrvrSeatPosGroupSaveSts` STRING,
    `DrvrSeatPosRclCmd` STRING,
    `DrvrSeatRclnPosRclCmd` STRING,
    `DrvrSeatRclnSwReq` STRING,
    `DrvrSeatRclnSwReq_SW_l` STRING,
    `DrvrSeatRclnSwReq_UI` STRING,
    `DrvrSeatTiltPosRclCmd` STRING,
    `DrvrSeatTiltSwReq` STRING,
    `DrvrSeatTiltSwReq_SW_l` STRING,
    `DrvrSeatTiltSwReq_UI` STRING,
    `DrvrSeatVertPosRclCmd` STRING,
    `DrvrSeatVertSwReq` STRING,
    `DrvrSeatVertSwReq_SW_l` STRING,
    `DrvrSeatVertSwReq_UI` STRING,
    `DrvrStrgDlvrdToq` STRING,
    `DrvrStrgDlvrdToqRddy` STRING,
    `DrvrStrgDlvrdToqV` STRING,
    `DrvrUseIDBradcst` STRING,
    `DrvrUseIDRoleInfo` STRING,
    `DrvrWndDclnSpc` STRING,
    `DspcOpenSts` STRING,
    `DstbtrInRefTemp` STRING,
    `DTCInfomationBMS` STRING,
    `DTCInfomationCCU` STRING,
    `DTCInfomationDCM_FL` STRING,
    `DTCInfomationEOPC` STRING,
    `DTCInfomationIMCU` STRING,
    `DTCInfomationSDM` STRING,
    `EAC_speed` STRING,
    `EACVol` STRING,
    `EASCCalibrationErr_l` STRING,
    `EASCPosGroupMmrySts` STRING,
    `EASCPosGroupRclReq` STRING,
    `EASCPosGroupRclSts` STRING,
    `EASCPosGroupSaveReq` STRING,
    `EASCPosGroupSaveSts` STRING,
    `EASCSysFltSts` STRING,
    `EasyEntMdInd` STRING,
    `EasyEntPosRclReq` STRING,
    `EasyEntPosRclSts` STRING,
    `EasyEntPosSaveReq` STRING,
    `EasyEntPosSpRclReq` STRING,
    `EasyLdMdInd` STRING,
    `EasyPssMdInd` STRING,
    `EBDA` STRING,
    `EBSAcumtdBatChrg` STRING,
    `EBSAcumtdBatDschrg` STRING,
    `EBSBatCrnt_l` STRING,
    `EBSBatCrnt_R_l` STRING,
    `EBSBatVol_l` STRING,
    `EBSBatVol_R_l` STRING,
    `EBSSOC_l` STRING,
    `EBSSOC_R_l` STRING,
    `EBSSOCSts_l` STRING,
    `EBSSOCSts_R_l` STRING,
    `EBSSOHOfLAM` STRING,
    `EBSSOHOfLAM_l` STRING,
    `EBSSOHOfLAMSts_l` STRING,
    `EBSSOHOfSlphtn` STRING,
    `EBSSOHOfSlphtn_l` STRING,
    `EBSSOHOfSlphtn_R_l` STRING,
    `EBSSOHOfSlphtnSts_l` STRING,
    `EBSTemSts_l` STRING,
    `EBSVolSts_l` STRING,
    `EDSPumpFaultInfo` STRING,
    `EDUOilPmpMotCrnt` STRING,
    `EDUOilPmpMotSpd` STRING,
    `EDUOilPmpMotSpdV` STRING,
    `EDUOilPmpMotSts` STRING,
    `EDUOilPmpMotVol` STRING,
    `EDUOilPmpMotVolV` STRING,
    `EDUOilTem` STRING,
    `ElecACSpd` STRING,
    `ElecCsumpPerKm` STRING,
    `ElecCsumpPerKmV` STRING,
    `ElecEnrgAvgRstPerfd` STRING,
    `EmgcBrkA` STRING,
    `EmgcCallFlrSts` STRING,
    `EnrgSplReq` STRING,
    `EnrgSplReqEPTCrkAbotd` STRING,
    `EnrgSplReqEPTCrkAbotdRsn` STRING,
    `EnrgSplReqEPTRdy` STRING,
    `EnrgSplReqEPTRunAbotd` STRING,
    `EnrgSplReqEPTRunAbotdRsn` STRING,
    `EnrgSplReqScene` STRING,
    `EnrgSplReqV` STRING,
    `EOPCEDUOilPmpMotCrntV` STRING,
    `EPBAppcnSts` STRING,
    `EPBAppcnStsRC` STRING,
    `EPBAvlblySts` STRING,
    `EPBFlrSts` STRING,
    `EPBSts` STRING,
    `EPBSwSts` STRING,
    `EPBSysStsIndReq` STRING,
    `EPBSysWrnngIndReq` STRING,
    `EPMCU_NKI_PTEXTDCAN` STRING,
    `EPMCUMotFltIO` STRING,
    `EPMCUParkngSts` STRING,
    `EPMCUSCUParkReqResp` STRING,
    `EPMOprtMd` STRING,
    `EPS_CHAvlbly` STRING,
    `EPS_NM_BSMtoRMS_CHCANFD` STRING,
    `EPS_NM_NOStoRMS_CHCANFD` STRING,
    `EPS_NM_PBSMtoRMS_CHCANFD` STRING,
    `EPS_NM_RMSSta_CHCANFD` STRING,
    `EPS_NM_RMStoNOS_CHCANFD` STRING,
    `EPS_NM_RSStoNOS_CHCANFD` STRING,
    `EPS_NM_RSStoRMS_CHCANFD` STRING,
    `EPSAIPkgAngReqResp` STRING,
    `EPSAIPkgAngReqRespInh` STRING,
    `EPSAIPltAngRespSts` STRING,
    `EPSAIPltAngRespStsCRC` STRING,
    `EPSAIPltAngRespStsInh` STRING,
    `EPSAIPltAngRespStsInhRddy` STRING,
    `EPSAIPltAngRespStsRC` STRING,
    `EPSAIPltAngRespStsRddy` STRING,
    `EPSAIPltAvlLvl` STRING,
    `EPSAIPltAvlLvlRddy` STRING,
    `EPSAIPltCpcty` STRING,
    `EPSAIPltCtrlChannel` STRING,
    `EPSAIPltCtrlChannelRddy` STRING,
    `EPSFlrSts` STRING,
    `EPTAccelActuPos` STRING,
    `EPTAccelActuPosV` STRING,
    `EPTAccelEfctvPos` STRING,
    `EPTACLdAld` STRING,
    `EPTBrkPdlDscrtInptSts` STRING,
    `EPTBrkPdlDscrtInptStsV` STRING,
    `EPTCoolngFanSts` STRING,
    `EPTCrkAbotd` STRING,
    `EPTFlt` STRING,
    `EPTFltLvl` STRING,
    `EPTHVDCDCMdReq` STRING,
    `EPTInfoDsp2` STRING,
    `EPTMainrelayDrvReq` STRING,
    `EPTMotClntFlt` STRING,
    `EPTRdy` STRING,
    `EPTRdyBkup` STRING,
    `EPTRgtnLvl` STRING,
    `EPTRunCrkTrmlSts` STRING,
    `EPTSAMMaxToqLmt` STRING,
    `EPTSAMMdReq` STRING,
    `EPTSAMSpdReq` STRING,
    `EPTSAMToqReq` STRING,
    `EPTSysPwrLmtA` STRING,
    `EPTTMMaxToqLmt` STRING,
    `EPTTMMaxToqLmtV` STRING,
    `EPTTMMdReq` STRING,
    `EPTTMSpdReq` STRING,
    `EPTTMToqReq` STRING,
    `EPTToqCustSetngDspCmd` STRING,
    `EPTTrInptShaftToq` STRING,
    `EPTTrOtptShaftToq` STRING,
    `EPTTrOtptShaftTotToq` STRING,
    `ErrRespHODM_l` STRING,
    `ESS_PTAvlbly` STRING,
    `ESSBscInfo` STRING,
    `ESSPTCHVILSts` STRING,
    `ESSPTCHVILStsV` STRING,
    `EvapACMangmntInfo` STRING,
    `EvapEXVActPosition` STRING,
    `EvtPotclSigCCU` STRING,
    `EvtPotclSigSCM` STRING,
    `ExtnlBrkPrio` STRING,
    `FasnDrvrSbltIndCmd` STRING,
    `FasnSecRowLSbltAtc` STRING,
    `FasnSecRowMidSbltAtc` STRING,
    `FasnSecRowRSbltAtc` STRING,
    `FasnThrdRowLSbltAtc` STRING,
    `FasnThrdRowMidSbltAtc` STRING,
    `FasnThrdRowRSbltAtc` STRING,
    `FCRSysSts` STRING,
    `FCTBSysFltSts` STRING,
    `FCWDspCmd` STRING,
    `FCWrnngSts` STRING,
    `FCWSysFltSts` STRING,
    `FDR_NKI_BKPCANFD` STRING,
    `FDRSts` STRING,
    `FICMRLSSnstvtLvlSts` STRING,
    `FICMSWHtngReq_l` STRING,
    `FLCrnRdrSts` STRING,
    `FLDCMDoorOpenSts` STRING,
    `FLDCMDoorOpenSts_L` STRING,
    `FLDCMFLWndLclSwSts` STRING,
    `FLDCMFRWndLclSwSts` STRING,
    `FLDCMMstrLckCtrlReq` STRING,
    `FLDCMOtsdMirFoldCmd` STRING,
    `FLDCMOtsdMirMmryCtrlPosStoC` STRING,
    `FLDCMRLWndLclSwSts` STRING,
    `FLDCMRRWndLclSwSts` STRING,
    `FLDoorCtrlModuOtsdMirFoldCmd` STRING,
    `FLDoorHadlSwA` STRING,
    `FLDoorOpenPos` STRING,
    `FLDoorOpenPosSetngReq` STRING,
    `FLDoorOpPosSetngDsplyCmd_l` STRING,
    `FLDoorOtsdAirTemCrVal_l` STRING,
    `FLDoorRdrSts_l` STRING,
    `FLHdrtPos` STRING,
    `FLHdrtPosRclCmd` STRING,
    `FLHdrtSwReq` STRING,
    `FLHdrtSwReq_SW_l` STRING,
    `FLHdrtSwReq_UI` STRING,
    `FLLegRetFwdPos` STRING,
    `FLLegRetFwdPosRclCmd` STRING,
    `fllegretfwdswreq` STRING,
    `FLLegRetFwdSwReq_SW_l` STRING,
    `FLLegRetFwdSwReq_UI` STRING,
    `FLObsRng` STRING,
    `FLOtsdHadlA` STRING,
    `FLPCPRDoorPrblm` STRING,
    `FLPODBCMCmd` STRING,
    `FLPODPrblm` STRING,
    `FLPWLStatus` STRING,
    `FLPwrDoorOpSts` STRING,
    `FLSeatBrkngSwReq_l` STRING,
    `FLSeatHeatLvl` STRING,
    `FLSeatHeatReq` STRING,
    `FLSeatHeatSysFlt` STRING,
    `FLSeatMsagOnOffSts` STRING,
    `FLSeatVentLvl` STRING,
    `FLSideObsDist` STRING,
    `FLSideObsRng` STRING,
    `FLSurndCamrSts` STRING,
    `FLTireIDCfgErr` STRING,
    `FLTireNoSig` STRING,
    `FLTirePrs` STRING,
    `FLTirePrsLvl` STRING,
    `FLTirePrsPcnt` STRING,
    `FLTirePrsV` STRING,
    `FLTireSts` STRING,
    `FLWndDclnSpc` STRING,
    `FLWndOpReq` STRING,
    `FOTAStatus` STRING,
    `FOTAStatusValid` STRING,
    `FOTATarget` STRING,
    `FOTATargetValid` STRING,
    `FRCrnRdrSts` STRING,
    `FRDCMDoorOpenSts` STRING,
    `FRDCMDoorOpenSts_L` STRING,
    `FRDCMMstrLckCtrlReq` STRING,
    `FRDCMOtsdMirMmryCtrlPosStoC` STRING,
    `FRDoorHadlSwA` STRING,
    `FRDoorOpenPos` STRING,
    `FRDoorOpenPosSetngReq` STRING,
    `FRDoorOpPosSetngDsplyCmd_l` STRING,
    `FRDoorOtsdAirTemCrVal_l` STRING,
    `FRDoorRdrSts_l` STRING,
    `FrgrFltSts` STRING,
    `FrgrWkngSts` STRING,
    `FrgrWkngSts_l` STRING,
    `FRHdrtPos` STRING,
    `FRLegRetFwdPos` STRING,
    `FRObsRng` STRING,
    `FROtsdHadlA` STRING,
    `FRPCPRDoorPrblm` STRING,
    `FRPODBCMCmd` STRING,
    `FRPODPrblm` STRING,
    `FRSeatBrkngSwReq_l` STRING,
    `FRSeatFoldSwtReq_l` STRING,
    `FRSeatHeatLvl` STRING,
    `FRSeatHeatReq` STRING,
    `FRSeatHeatSysFlt` STRING,
    `FRSeatMsagOnOffSts` STRING,
    `FRSeatVentLvl` STRING,
    `FRSideObsDist` STRING,
    `FRSideObsRng` STRING,
    `FRSM_NKI_BDCAN` STRING,
    `FRSurndCamrSts` STRING,
    `FRTireIDCfgErr` STRING,
    `FRTireNoSig` STRING,
    `FRTirePrs` STRING,
    `FRTirePrsLvl` STRING,
    `FRTirePrsPcnt` STRING,
    `FRTirePrsV` STRING,
    `FRTireSts` STRING,
    `FrtLeftObstacleDist` STRING,
    `FrtMidLObsRng` STRING,
    `FrtMidRObsRng` STRING,
    `FrtObsDist` STRING,
    `FrtObstacleDist` STRING,
    `FrtPDCAudWrnng` STRING,
    `FrtPlacardCfgErr` STRING,
    `FrtPnrmCamrSts` STRING,
    `FrtPsgnAirbagSwSts` STRING,
    `FrtPsngAirbagDsblIndF` STRING,
    `FrtPsngAirbagEnbIndF` STRING,
    `FrtPsngDoorOpenSts` STRING,
    `FrtPsngOccupntSts` STRING,
    `FrtPsngPWLInitnRmndr` STRING,
    `FrtPsngSbltAtc` STRING,
    `FrtPsngWndDclnSpc` STRING,
    `FrtRdrSts` STRING,
    `FrtRightObstacleDist` STRING,
    `FrtSideLghtF` STRING,
    `FrtSurndCamr1Sts` STRING,
    `FrtSurndCamr2Sts` STRING,
    `FrtUss1FltSts` STRING,
    `FrtUss2FltSts` STRING,
    `FrtUss3FltSts` STRING,
    `FrtUss4FltSts` STRING,
    `FrtWiperMdSts` STRING,
    `FrtWiperOpReq` STRING,
    `FrtWiperParkPosA` STRING,
    `FrtWiperSwSts` STRING,
    `FRWndDclnSpc` STRING,
    `FRWndOpReq` STRING,
    `HDCSysSts` STRING,
    `HeadingRTK` STRING,
    `HeatPumpErMngmntInfo` STRING,
    `HeatPumpMd` STRING,
    `HOD_FrP00_Checksum_l` STRING,
    `HOD_FrP00_RolingCounter_l` STRING,
    `HODDetnSts` STRING,
    `HODDetnSts_l` STRING,
    `HODDetSts` STRING,
    `HODdiagSts` STRING,
    `HODdiagSts_l` STRING,
    `HODMHardVer_l` STRING,
    `HODMSoftVer_l` STRING,
    `HODSysSts` STRING,
    `HODSysSts_l` STRING,
    `HODTchZone1Sts` STRING,
    `HODTchZone1Sts_l` STRING,
    `HODTchZone1Val` STRING,
    `HODTchZone1Val_l` STRING,
    `HODTchZone2Sts_l` STRING,
    `HODTchZone2Val_l` STRING,
    `HODTchZone3Sts_l` STRING,
    `HODTchZone3Val_l` STRING,
    `HPCRationalFaultInfo` STRING,
    `HtrPumpFaultInfo` STRING,
    `HVACRationalFaultInfo` STRING,
    `HVBatElecEnrgAvgRate` STRING,
    `HVBatElecEnrgAvgRateV` STRING,
    `HVBatShutOff` STRING,
    `HVDCDCActvDisOverTime` STRING,
    `HVDCDCClntPumpPWMReq` STRING,
    `HVDCDCDrtng` STRING,
    `HVDCDCHdwrFlr` STRING,
    `HVDCDCHVSideCrnt` STRING,
    `HVDCDCHVSideCrntV` STRING,
    `HVDCDCHVSideVol` STRING,
    `HVDCDCHVSideVolV` STRING,
    `HVDCDCIntnlFlr` STRING,
    `HVDCDCLdRatioV` STRING,
    `HVDCDCLVSideCrnt` STRING,
    `HVDCDCLVSideCrntV` STRING,
    `HVDCDCLVSideStptFdbk` STRING,
    `HVDCDCLVSideStptFdbkV` STRING,
    `HVDCDCLVSideVol` STRING,
    `HVDCDCLVSideVolV` STRING,
    `HVDCDCOverCurrProtLV` STRING,
    `HVDCDCOverHtd` STRING,
    `HVDCDCSta` STRING,
    `HVDCDCTem` STRING,
    `HVDCHVILSts` STRING,
    `HVDCHVILStsV` STRING,
    `HVEstbCond` STRING,
    `HVFailRsn` STRING,
    `IAM_NKI_CONNCANFD` STRING,
    `IAM_NOI_CONNCANFD` STRING,
    `IAM_NWI_CONNCANFD` STRING,
    `IAMAvlbly` STRING,
    `IBS_NKI_CHCANFD` STRING,
    `IBS_NKI_SFCANFD` STRING,
    `IBS_NOI_CHCANFD` STRING,
    `IBS_NOI_SFCANFD` STRING,
    `IBS_NWI_CHCANFD` STRING,
    `IBS_NWI_SFCANFD` STRING,
    `ICC_NKI_BDCAN` STRING,
    `ICC_NKI_BDEXTDCAN` STRING,
    `ICC_NKI_BKPCANFD` STRING,
    `ICC_NKI_CHCANFD` STRING,
    `ICC_NKI_CONNCANFD` STRING,
    `ICC_NKI_INFOCAN` STRING,
    `ICC_NKI_INFOCANFD` STRING,
    `ICC_NKI_PTCANFD` STRING,
    `ICC_NKI_PTEXTDCAN` STRING,
    `ICC_NKI_SFCANFD` STRING,
    `ICC_NOI_BDCAN` STRING,
    `ICC_NOI_BDEXTDCAN` STRING,
    `ICC_NOI_BKPCANFD` STRING,
    `ICC_NOI_CHCANFD` STRING,
    `ICC_NOI_CONNCANFD` STRING,
    `ICC_NOI_INFOCAN` STRING,
    `ICC_NOI_INFOCANFD` STRING,
    `ICC_NOI_PTCANFD` STRING,
    `ICC_NOI_PTEXTDCAN` STRING,
    `ICC_NOI_SFCANFD` STRING,
    `ICC_NWI_BDCAN` STRING,
    `ICC_NWI_BDEXTDCAN` STRING,
    `ICC_NWI_BKPCANFD` STRING,
    `ICC_NWI_CHCANFD` STRING,
    `ICC_NWI_CONNCANFD` STRING,
    `ICC_NWI_INFOCAN` STRING,
    `ICC_NWI_INFOCANFD` STRING,
    `ICC_NWI_PTCANFD` STRING,
    `ICC_NWI_PTEXTDCAN` STRING,
    `ICC_NWI_SFCANFD` STRING,
    `ICCRmtSeatHeatFlrRsn` STRING,
    `ICCRmtSeatHeatSts` STRING,
    `ICCRmtStrgWhlHeatFlrRsn` STRING,
    `ICCRmtStrgWhlHeatSts` STRING,
    `ICM_InfoAvlbly` STRING,
    `ICM_InfoCANFDAvlbly` STRING,
    `ICM_NKI_INFOCAN` STRING,
    `ICM_NKI_INFOCANFD` STRING,
    `ICM_NOI_INFOCAN` STRING,
    `ICM_NOI_INFOCANFD` STRING,
    `ICM_NWI_INFOCAN` STRING,
    `ICM_NWI_INFOCANFD` STRING,
    `ICMErSts` STRING,
    `ICMFLDoorOpenPosSetngDsplyCmd` STRING,
    `ICMFLDoorOpenPosSetngReq_UI` STRING,
    `ICMFRDoorOpenPosSetngDsplyCmd` STRING,
    `ICMFRDoorOpenPosSetngReq_UI` STRING,
    `ICMRLDoorOpenPosSetngDsplyCmd` STRING,
    `ICMRLDoorOpenPosSetngReq_UI` STRING,
    `ICMRRDoorOpenPosSetngDsplyCmd` STRING,
    `ICMRRDoorOpenPosSetngReq_UI` STRING,
    `ICMTurnOnRdyStsX` STRING,
    `IDigKeyExeAvlbl` STRING,
    `IDigKeyExeAvlblV` STRING,
    `IEasyEntSetSts` STRING,
    `IECU_NKI_CHCANFD` STRING,
    `IECU_NKI_SFTYCANFD` STRING,
    `IgnOffTime` STRING,
    `IMATE_NKI_INFOCAN` STRING,
    `IMATE_NWI_INFOCAN` STRING,
    `IMATESysFltRsn` STRING,
    `IMCU_025ms_Group05_RC` STRING,
    `IMCU_ExtdAvlbly` STRING,
    `IMCU_NKI_PTCANFD` STRING,
    `IMCU_NKI_PTEXTDCAN` STRING,
    `IMCU_NOI_PTCANFD` STRING,
    `IMCU_NOI_PTEXTDCAN` STRING,
    `IMCU_NWI_PTCANFD` STRING,
    `IMCU_NWI_PTEXTDCAN` STRING,
    `IMCU_PTAvlbly` STRING,
    `IMCUChrgTrgtSOCDspCmd` STRING,
    `IMCUCrntAvgElecCsump` STRING,
    `IMCUCrntAvgElecCsumpV` STRING,
    `IMCUElecCsumpPerKm` STRING,
    `IMCUElecCsumpPerKmV` STRING,
    `IMCUHVBatElecEnrgAvgRate` STRING,
    `IMCUKeyStatus` STRING,
    `IMCUReserCtrlDspCmd` STRING,
    `IMCUReserSpHourDspCmd` STRING,
    `IMCUReserSpMinuteDspCmd` STRING,
    `IMCUReserStHourDspCmd` STRING,
    `IMCUReserStMinuteDspCmd` STRING,
    `IMCUVehActuElecCsump` STRING,
    `IMCUVehActuElecCsumpV` STRING,
    `IMCUVehElecRng` STRING,
    `IMCUVehElecRngDspCmd` STRING,
    `IMCUVehElecRngV` STRING,
    `ImmoSetA` STRING,
    `IMU_GNSS_Signal_Status` STRING,
    `InsdAirPrtclMtrCDC` STRING,
    `IPD_CHAvlbly` STRING,
    `IPD_NKI_BKPCANFD` STRING,
    `IPD_NKI_CHCANFD` STRING,
    `IPD_NKI_SFCANFD` STRING,
    `IPD_NWI_BKPCANFD` STRING,
    `IPD_NWI_CHCANFD` STRING,
    `IPD_NWI_SFCANFD` STRING,
    `IPDLDWLKADspCmd` STRING,
    `IPDLDWLKALVsulznReq` STRING,
    `IPDLDWLKARVsulznReq` STRING,
    `IPDLDWSysSts` STRING,
    `IPDThermalWrnngLvl` STRING,
    `IPKDErSts` STRING,
    `ITMSACComprMngmntInfo` STRING,
    `ITMSACStatus` STRING,
    `ITMSAGS1Pos` STRING,
    `ITMSAGSSelfLearnSts` STRING,
    `ITMSBatteryWarmStatus` STRING,
    `ITMSBattReq` STRING,
    `ITMSBMSClntPumpFltInfo` STRING,
    `ITMSBMSPumpActuPWM` STRING,
    `ITMSEDSPumpActuPWM` STRING,
    `ITMSEDUClntInltTem` STRING,
    `ITMSHeatPumpErMngmntInfo` STRING,
    `ITMSHeatPumpMd` STRING,
    `ITMSHeatRecyMod` STRING,
    `ITMSHtrPumpActuPWM` STRING,
    `ITMSOnRutBattWarmSwDsp` STRING,
    `ITMSSxVlvActPos` STRING,
    `ITMSThrVlvActPWM` STRING,
    `ITMSThrVlvSelfLearnSts` STRING,
    `ITMSTripBookACSts` STRING,
    `ITMSTripBookBatteryWarmSts` STRING,
    `ITMSTripBookHour` STRING,
    `ITMSTripBookMinute` STRING,
    `ITMSTripBookSts` STRING,
    `ITMSTripBookWeek` STRING,
    `ITMSTripBookWeekly` STRING,
    `ITMSWPTCActuPwr` STRING,
    `ITMSWPTCOutTem` STRING,
    `keep_network_AMR` STRING,
    `keep_network_BPEPS` STRING,
    `keep_network_BPEPS_UB` STRING,
    `keep_network_MSM` STRING,
    `keep_network_MSM_Psng` STRING,
    `keep_network_PLCM` STRING,
    `KeepPTRdyReq` STRING,
    `Key1Loctn` STRING,
    `Key2Loctn` STRING,
    `KeyDetIndx` STRING,
    `KeyFobFLPwrDrCtrlSwA` STRING,
    `KeyFobFRPwrDrCtrlSwA` STRING,
    `KeyFobRLPwrDrCtrlSwA` STRING,
    `KeyFobRRPwrDrCtrlSwA` STRING,
    `LaneChngSts` STRING,
    `Latitude` STRING,
    `LBrkLghtF` STRING,
    `LBSDAndLCAWrnng` STRING,
    `lckngsysstsind` STRING,
    `LckngSysStsInd_FR_l` STRING,
    `LckngSysStsInd_l` STRING,
    `LDipdBeamLghtF` STRING,
    `LDircnIndLghtF` STRING,
    `LDircnIO` STRING,
    `LDOWWrnng` STRING,
    `LDrvnWhlRotlDircn` STRING,
    `LdspcOpenSts` STRING,
    `LDWLKADspCmd` STRING,
    `LDWLKALVsulznReq` STRING,
    `LDWLKARVsulznReq` STRING,
    `LDWSysFltSts` STRING,
    `LDWSysSts` STRING,
    `LevelMode_Request_IEnt` STRING,
    `LFSDA_NKI_BKPCANFD` STRING,
    `LFSDASts` STRING,
    `LghtSwPosSts` STRING,
    `LHCMSHoztPos` STRING,
    `LHCMSHoztPosRclReq` STRING,
    `LHCMSMmryCtrlPosRclSts` STRING,
    `LHCMSVertPos` STRING,
    `LHCMSVertPosRclReq` STRING,
    `LHRDA_NKI_BKPCANFD` STRING,
    `LiftMdInd` STRING,
    `LLidarSts` STRING,
    `LNonDrvnWhlRotlDircn` STRING,
    `Longitude` STRING,
    `LOtsdMirHoztPos` STRING,
    `LOtsdMirHoztPosRclReq` STRING,
    `LOtsdMirMmryCtrlPosRclSts` STRING,
    `LOtsdMirVertPos` STRING,
    `LOtsdMirVertPosRclReq` STRING,
    `LowWiperWshrFludLvlSwA` STRING,
    `LPnrmCamrSts` STRING,
    `LScrnLftrSwReq_l` STRING,
    `LSideLghtF` STRING,
    `LUss1FltSts` STRING,
    `LUss2FltSts` STRING,
    `lvbm_group03_rc` STRING,
    `LVBMAlrmSts` STRING,
    `LVBMCrnt` STRING,
    `LVBMFltLvl` STRING,
    `LVBMHdwrFltSts` STRING,
    `LVBMMOSSts` STRING,
    `LVBMSOC` STRING,
    `LVBMSOCV` STRING,
    `LVBMSOH` STRING,
    `LVBMSOHV` STRING,
    `LVBMTotVol` STRING,
    `LVentFootTem` STRING,
    `LvlCtrlAdjActn` STRING,
    `ILvlCtrlAdjSts` STRING,
    `LvlCtrlHghtSnsrCalCmpd` STRING,
    `LvlCtrlReqdTrgtLvl` STRING,
    `LvlCtrlRestrctnRsn` STRING,
    `LvlCtrlVehMd` STRING,
    `MainBeamLghtOn` STRING,
    `MainBeamSwPosSts` STRING,
    `MdFlapActuAirmixAngL` STRING,
    `MdFlapActuAirmixAngR` STRING,
    `MdOfPwrMd` STRING,
    `MixAirLftMotFaultInfo` STRING,
    `MixAirRtMotFaultInfo` STRING,
    `MSM_NKI_BDCAN` STRING,
    `MSMDrvrSeatHoztMotActn` STRING,
    `MSMDrvrSeatHoztPos` STRING,
    `MSMDrvrSeatRclnMotActn` STRING,
    `MSMDrvrSeatRclnPos` STRING,
    `MSMDrvrSeatTiltMotActn` STRING,
    `MSMDrvrSeatTiltPos` STRING,
    `MSMDrvrSeatVertMotActn` STRING,
    `MSMDrvrSeatVertPos` STRING,
    `MSMDrvrSeatZeroPosLrnngSts` STRING,
    `MSMPsngSeatHoztPos` STRING,
    `MSMPsngSeatRclnPos` STRING,
    `MSMPsngSeatVertPos` STRING,
    `MSMPsngSeatZeroPosLrnngSts` STRING,
    `MSRA` STRING,
    `NarwSpcAstBtnResp` STRING,
    `NarwSpcAstBtnSts` STRING,
    `NarwSpcAstSwSts` STRING,
    `network_mode` STRING,
    `NFCIDNum1` STRING,
    `NFCIDNum2` STRING,
    `NFCIDNum3` STRING,
    `NFCIDNum4` STRING,
    `NFCIDNum5` STRING,
    `NFCInCardIDE` STRING,
    `NFCOutCardIDE` STRING,
    `NFCSMPlmntSts` STRING,
    `NFCSMSta` STRING,
    `NOAQukSwSts` STRING,
    `NOASwSts` STRING,
    `NRCDToqReqSts` STRING,
    `NRCDToqReqVal` STRING,
    `Obj0LatDistance` STRING,
    `Obj0LatSpd` STRING,
    `Obj0LongDistance` STRING,
    `Obj0LongSpd` STRING,
    `Obj0Style` STRING,
    `OCondOutRefTemp` STRING,
    `OfbdChrgrNgtvSktTem` STRING,
    `OfbdChrgrNgtvSktTemV` STRING,
    `OfbdChrgrPstvSktTem` STRING,
    `OHXEXVActPosition` STRING,
    `OilPmpMotFltLvl` STRING,
    `OnBdChrgrAltrCrntInptCrnt` STRING,
    `OnbdChrgrAltrCrntInptHVCrntLmt` STRING,
    `OnBdChrgrAltrCrntInptVol` STRING,
    `OnBdChrgrCtrlPilotPWMDuty` STRING,
    `OnBdChrgrCtrlPilotPWMSts` STRING,
    `OnBdChrgrFltCt` STRING,
    `OnBdChrgrFltSts` STRING,
    `OnBdChrgrHVILStsVal` STRING,
    `OnBdChrgrInsdTem3` STRING,
    `OnBdChrgrL1AltrCrntInptCrnt` STRING,
    `OnBdChrgrL1AltrCrntInptVol` STRING,
    `OnBdChrgrL2AltrCrntInptCrnt` STRING,
    `OnBdChrgrL2AltrCrntInptVol` STRING,
    `OnBdChrgrL3AltrCrntInptCrnt` STRING,
    `OnBdChrgrL3AltrCrntInptVol` STRING,
    `OnBdChrgrLastWkup` STRING,
    `OnbdChrgrLLCMaxTem` STRING,
    `OnbdChrgrOpngMd` STRING,
    `OnbdChrgrOtptCrntVal` STRING,
    `OnBdChrgrOtptVolV` STRING,
    `OnbdChrgrOtptVolVal` STRING,
    `OnbdChrgrPFCMaxTem` STRING,
    `OnbdChrgrSktNgtvSnsrTem` STRING,
    `OnBdChrgrSktPstvSnsrTem` STRING,
    `OnBdChrgrSktPstvSnsrTemV` STRING,
    `OnBdChrgrSts` STRING,
    `OnBdChrgrWkup` STRING,
    `OtrIntlgntMdTrigHvacReq` STRING,
    `OtrIntlgntMdTrigReq` STRING,
    `OtsAirTmp` STRING,
    `OtsdAirTemCrVal` STRING,
    `OtsdMirPosGroupMmrySts` STRING,
    `OtsdMirPosGroupRclReq` STRING,
    `OtsdMirPosGroupRclSts` STRING,
    `OtsdMirPosGroupSaveReq` STRING,
    `OtsdMirPosGroupSaveSts` STRING,
    `OtsdMirPosRclReq` STRING,
    `PDCSysSts` STRING,
    `PDUHVILSts` STRING,
    `PDUHVILStsV` STRING,
    `PEBOfbdChrgCapVol` STRING,
    `PedtrnProtnSysIndrCmd` STRING,
    `PEPSAntFlt` STRING,
    `PGM_NKI_INFOCAN` STRING,
    `PLCM_NOI_BDCAN` STRING,
    `PLCM_NWI_BDCAN` STRING,
    `PLCMLdspcOpenSts` STRING,
    `PMAFLObsRng` STRING,
    `PMAFLSideObsDist` STRING,
    `PMAFLSideObsDistV` STRING,
    `PMAFLSideObsRng` STRING,
    `PMAFRObsRng` STRING,
    `PMAFRSideObsDist` STRING,
    `PMAFRSideObsDistV` STRING,
    `PMAFRSideObsRng` STRING,
    `PMAFrtMidLObsRng` STRING,
    `PMAFrtMidRObsRng` STRING,
    `PMAFrtObsDist` STRING,
    `PMAFrtObsDistV` STRING,
    `PMAPDCSysSts` STRING,
    `PMARLObsRng` STRING,
    `PMARLSideObsDist` STRING,
    `PMARLSideObsDistV` STRING,
    `PMARLSideObsRng` STRING,
    `PMARrMidLObsRng` STRING,
    `PMARrMidRObsRng` STRING,
    `PMARrObsDist` STRING,
    `PMARrObsDistV` STRING,
    `PMARRObsRng` STRING,
    `PMARRSideObsDist` STRING,
    `PMARRSideObsDistV` STRING,
    `PMARRSideObsRng` STRING,
    `PMDCSta` STRING,
    `PollngSts` STRING,
    `PositionAttitudeStatus` STRING,
    `PowerOnReset` STRING,
    `PsngAutoAirVentSwngSts` STRING,
    `PsngAvdAirVentSts` STRING,
    `PsngEasyEntPosRclSts` STRING,
    `PsngFcsOnAirVentSts` STRING,
    `PsngHdrtPosLkagRclCmd` STRING,
    `PsngLegRetFwdPosLkagRclCmd` STRING,
    `PsngPosSaveReq` STRING,
    `PsngSeatHoztPosLkagRclCmd` STRING,
    `PsngSeatHoztPosRclCmd` STRING,
    `PsngSeatHoztSwReq` STRING,
    `PsngSeatHoztSwReq_SW_l` STRING,
    `PsngSeatHoztSwReq_UI` STRING,
    `PsngSeatLkagRclnPosLkagRclCmd` STRING,
    `PsngSeatPosRclCmd` STRING,
    `PsngSeatPosRclReq` STRING,
    `PsngSeatPosRclSts` STRING,
    `PsngSeatRclnPosRclCmd` STRING,
    `PsngSeatRclnSwReq` STRING,
    `PsngSeatRclnSwReq_SW_l` STRING,
    `PsngSeatRclnSwReq_UI` STRING,
    `PsngSeatVertPosLkagRclCmd` STRING,
    `PsngSeatVertPosRclCmd` STRING,
    `PsngSeatVertPosRclCmd_UI` STRING,
    `PsngSeatVertSwReq` STRING,
    `PsngSeatVertSwReq_SW_l` STRING,
    `PsngSeatVertSwReq_UI` STRING,
    `PtACCToqReqResp` STRING,
    `PTCActuPwr` STRING,
    `PTCMngmntInfo` STRING,
    `PwrLftgtPos` STRING,
    `PwrLftgtPosSetngReq` STRING,
    `PwrLftgtPosV` STRING,
    `PwrLftgtSts` STRING,
    `PwrLftgtSwIndrCmd_l` STRING,
    `PwrLftgtSwReq_l` STRING,
    `PyrofuseInfo1_DA04` STRING,
    `PyrofuseInfo2_DA04` STRING,
    `PyrofuseInfo3_DA04` STRING,
    `PyrofuseInfoIndex_DA04` STRING,
    `RBM_NKI_CHCANFD` STRING,
    `RBM_NKI_SFCANFD` STRING,
    `RBrkLghtF` STRING,
    `RBSDAndLCAWrnng` STRING,
    `RC_Bluetooth` STRING,
    `RCTBSysFltSts` STRING,
    `RCWSelSts` STRING,
    `RCWWrnng` STRING,
    `RDASysSta` STRING,
    `RDipdBeamLghtF` STRING,
    `RDircnIndLghtF` STRING,
    `RDircnIO` STRING,
    `RDOWWrnng` STRING,
    `RDrvnWhlRotlDircn` STRING,
    `RdyFailRsn` STRING,
    `RearLeftObstacleDist` STRING,
    `RearObstacleDist` STRING,
    `RearRightObstacleDist` STRING,
    `RevsLghtF` STRING,
    `RFICRebootCnt` STRING,
    `RFICRebootReason` STRING,
    `RFReceiveMode` STRING,
    `RFSDA_NKI_BKPCANFD` STRING,
    `RFSDASts` STRING,
    `RFWelcomeMode` STRING,
    `RgnLvReqFICM` STRING,
    `RgstnPltLghtF` STRING,
    `RgtvBrkFnSts` STRING,
    `RHCMSHoztPos` STRING,
    `RHCMSHoztPosRclReq` STRING,
    `RHCMSMmryCtrlPosRclSts` STRING,
    `RHCMSVertPos` STRING,
    `RHCMSVertPosRclReq` STRING,
    `RHRDA_NKI_BKPCANFD` STRING,
    `RHRDAAvlbly` STRING,
    `RHRDASts` STRING,
    `RLChildProtnA` STRING,
    `RLChildProtnSts` STRING,
    `RLChildProtnThrmlSts` STRING,
    `RLCrnRdrSts` STRING,
    `RLDCMDoorOpenSts` STRING,
    `RLDCMMstrLckCtrlReq` STRING,
    `RLDoorHadlSwA` STRING,
    `RLDoorOpenPos` STRING,
    `RLDoorOpenPosSetngReq` STRING,
    `RLDoorOpenSts` STRING,
    `RLDoorOpPosSetngDsplyCmd_l` STRING,
    `RLDoorRdrSts_l` STRING,
    `RLidarSts` STRING,
    `RLObsRng` STRING,
    `RLOtsdHadlA` STRING,
    `RLPCPRDoorPrblm` STRING,
    `RLPODBCMCmd` STRING,
    `RLPODPrblm` STRING,
    `RLPWLInitnRmndr` STRING,
    `RLSideObsDist` STRING,
    `RLSideObsRng` STRING,
    `RLSurndCamrlSts` STRING,
    `RLTireIDCfgErr` STRING,
    `RLTirePrs` STRING,
    `RLTirePrsLvl` STRING,
    `RLTirePrsPcnt` STRING,
    `RLTirePrsV` STRING,
    `RLTireSts` STRING,
    `RLWndDclnSpc` STRING,
    `RLWndLclSwSts` STRING,
    `RLWndOpReq` STRING,
    `RmnDrvngDist` STRING,
    `RmtACAbotRsn` STRING,
    `RmtACReq` STRING,
    `RmtACRmningTime` STRING,
    `RmtACSts` STRING,
    `RmtACTrgtTemReq` STRING,
    `RmtScurReq` STRING,
    `RmtScurResp` STRING,
    `RNonDrvnWhlRotlDircn` STRING,
    `ROtsdMirHoztPos` STRING,
    `ROtsdMirHoztPosRclReq` STRING,
    `ROtsdMirMmryCtrlPosRclSts` STRING,
    `ROtsdMirVertPos` STRING,
    `ROtsdMirVertPosRclReq` STRING,
    `RPnrmCamrCalSts` STRING,
    `RPnrmCamrSts` STRING,
    `RrACOnOffDspCmd` STRING,
    `RRChildProtnA` STRING,
    `RRChildProtnSts` STRING,
    `RRChildProtnThrmlSts` STRING,
    `RRCrnRdrSts` STRING,
    `RRDCMDoorOpenSts` STRING,
    `RRDCMMstrLckCtrlReq` STRING,
    `RRDoorHadlSwA` STRING,
    `RRDoorOpenPos` STRING,
    `RRDoorOpenPosSetngReq` STRING,
    `RRDoorOpenSts` STRING,
    `RRDoorOpPosSetngDsplyCmd_l` STRING,
    `RRDoorRdrSts_l` STRING,
    `RrFogLghtF` STRING,
    `RrMidLObsRng` STRING,
    `RrMidRObsRng` STRING,
    `RrObsDist` STRING,
    `RRObsRng` STRING,
    `RROtsdHadlA` STRING,
    `RRPCPRDoorPrblm` STRING,
    `RrPDCAudWrnng` STRING,
    `RrPnrmCamrCalSts` STRING,
    `RrPnrmCamrSts` STRING,
    `RRPODBCMCmd` STRING,
    `RRPODPrblm` STRING,
    `RRPWLInitnRmndr` STRING,
    `RrRSurndCamrSts` STRING,
    `RrSideLghtF` STRING,
    `RRSideObsDist` STRING,
    `RRSideObsRng` STRING,
    `RrSurndCamrSts` STRING,
    `RRTireIDCfgErr` STRING,
    `RRTireNoSig` STRING,
    `RRTirePrs` STRING,
    `RRTirePrsLvl` STRING,
    `RRTirePrsPcnt` STRING,
    `RRTirePrsV` STRING,
    `RRTireSts` STRING,
    `RrtPlacardCfgErr` STRING,
    `RrUss1FltSts` STRING,
    `RrUss2FltSts` STRING,
    `RrUss3FltSts` STRING,
    `RrUss4FltSts` STRING,
    `RRWndDclnSpc` STRING,
    `RRWndLclSwSts` STRING,
    `RRWndOpReq` STRING,
    `RScrnLftrSwReq_l` STRING,
    `RSeatPosLkagRclCmd` STRING,
    `RSeatPosLkagRclSts` STRING,
    `RSideLghtF` STRING,
    `RUss1FltSts` STRING,
    `RUss2FltSts` STRING,
    `RVentFootTem` STRING,
    `RVSEPTCrkAbotd` STRING,
    `RVSEPTCrkAbotdRsn` STRING,
    `RVSEPTRdy` STRING,
    `RVSEPTRunAbotd` STRING,
    `RVSEPTRunAbotdRsn` STRING,
    `RVSReq` STRING,
    `RVSReqA` STRING,
    `RVSSts` STRING,
    `RWSGW_NKI_CHCANFD` STRING,
    `RWSWarning` STRING,
    `S11LFLSeatHeatLvl` STRING,
    `S11LFLSeatHeatReq` STRING,
    `S11LFLSeatHeatSysFlt` STRING,
    `S11LFRSeatHeatLvl` STRING,
    `S11LFRSeatHeatReq` STRING,
    `S11LFRSeatHeatSysFlt` STRING,
    `S11LSecRowLSeatHeatLvl` STRING,
    `S11LSecRowLSeatHeatReq` STRING,
    `S11LSecRowLSeatHeatSysFlt` STRING,
    `S11LSecRowRSeatHeatLvl` STRING,
    `S11LSecRowRSeatHeatReq` STRING,
    `S11LSecRowRSeatHeatSysFlt` STRING,
    `s11pwrlftgtswreq` STRING,
    `SAC_NKI_PTCANFD` STRING,
    `SAC_NKI_PTEXTDCAN` STRING,
    `SAMActuToq` STRING,
    `SAMFltLamp` STRING,
    `SAMFltLvlSts` STRING,
    `SAMHVILSts` STRING,
    `SAMHVILStsBkup` STRING,
    `SAMInvtrCrnt` STRING,
    `SAMInvtrVol` STRING,
    `SAMMaxAvlblToq` STRING,
    `SAMSpd` STRING,
    `SAMSta` STRING,
    `SAMSttrTem` STRING,
    `SAS_NKI_CHCANFD` STRING,
    `SatNoInPositionRTK` STRING,
    `SatNum` STRING,
    `SCM_NKI_CHCANFD` STRING,
    `scmscsmdreq` STRING,
    `SCSAPAAcclA` STRING,
    `SCSVehHoldA` STRING,
    `SCU_NKI_PTEXTDCAN` STRING,
    `SCU_NWI_PTEXTDCAN` STRING,
    `SCUExtdShifterFlr` STRING,
    `SCUParkLckReq` STRING,
    `SCUParkLckReqEPMCU` STRING,
    `ScurReqAC` STRING,
    `ScurReqADS` STRING,
    `ScurReqBCM` STRING,
    `ScurRespAC` STRING,
    `ScurRespADS` STRING,
    `ScurRespBCM` STRING,
    `ScurtAlrmSts` STRING,
    `ScurtAlrmTrigd` STRING,
    `ScurtKeyBatLow` STRING,
    `ScurtKeyInvd` STRING,
    `SCUShifterLvrPos` STRING,
    `SCUShifterLvrRawPos` STRING,
    `SDM_NKI_SFCANFD` STRING,
    `SDM_NWI_SFCANFD` STRING,
    `SDMAvlbly` STRING,
    `SDMRC` STRING,
    `SeatOccptnNum` STRING,
    `SecRowLOccupntSts` STRING,
    `SecRowLSeatHeatLvl` STRING,
    `SecRowLSeatHeatReq` STRING,
    `SecRowLSeatHeatSysFlt` STRING,
    `SecRowMOccupntSts` STRING,
    `SecRowROccupntSts` STRING,
    `SecRowRSeatHeatLvl` STRING,
    `SecRowRSeatHeatReq` STRING,
    `SecRowRSeatHeatSysFlt` STRING,
    `SecsOfMinute` STRING,
    `SecyAxleSts` STRING,
    `SLIFSts` STRING,
    `SolarStrngRSide` STRING,
    `StrgWhlAng` STRING,
    `StrgWhlAngGrd` STRING,
    `StrgWhlAngSnsrCalSts` STRING,
    `StrgWhlAngSnsrFlt` STRING,
    `StrgWhlAngV` STRING,
    `SusDmpngCtrlCustSetngDspCmd` STRING,
    `SusDmpngCtrlFlrSts` STRING,
    `SusDmpngCtrlLampReq` STRING,
    `SusDmpngCtrlMdSts` STRING,
    `SusFlrSts` STRING,
    `SusHghtFL` STRING,
    `SusHghtFLV` STRING,
    `SusHghtFR` STRING,
    `SusHghtFRV` STRING,
    `SusHghtRL` STRING,
    `SusHghtRLV` STRING,
    `SusHghtRR` STRING,
    `SusHghtRRV` STRING,
    `SusLvlCtrlCustSetngDspCmd` STRING,
    `SusLvlCtrlFlrSts` STRING,
    `SusLvlCtrlHghtSts` STRING,
    `SusLvlCtrlLampReq` STRING,
    `SvGroup` STRING,
    `SWHtngReqDspCmd` STRING,
    `SWHtngReqDspCmd_l` STRING,
    `SWHtngWrng_l` STRING,
    `SWVFaultInfo` STRING,
    `SysOpnlMd` STRING,
    `SysPwrMd` STRING,
    `SysPwrMd_l` STRING,
    `SysPwrMd_l_DCM_FL` STRING,
    `SysPwrMd_l_DCMFR_LIN` STRING,
    `SysPwrMd_l_ICC_LIN1` STRING,
    `SysPwrMdV` STRING,
    `SysVol` STRING,
    `TakeKeyOutRmndr` STRING,
    `TBOXSysTim` STRING,
    `TCSA` STRING,
    `TCSEnbd` STRING,
    `TCSOpngMd` STRING,
    `TCSOpngSts` STRING,
    `TeleMotPosCmd_l` STRING,
    `TeleMotPosSts` STRING,
    `TeleMotPosSts_l` STRING,
    `TiltMotPosCmd_l` STRING,
    `TiltMotPosSts` STRING,
    `TiltMotPosSts_l` STRING,
    `TMActuToq` STRING,
    `TMFltLamp` STRING,
    `TMFltLvlSts` STRING,
    `TMHVILStsBkup` STRING,
    `TMInvtrCrnt` STRING,
    `TMInvtrOvTempAlrm` STRING,
    `TMInvtrTem` STRING,
    `TMInvtrVol` STRING,
    `TMInvtrVolV` STRING,
    `TMMaxAvlblToq` STRING,
    `TMMaxAvlblToqV` STRING,
    `TMRtrTem` STRING,
    `TMSpd` STRING,
    `TMSta` STRING,
    `TMStrOvTempAlrm` STRING,
    `TMSttrTem` STRING,
    `TMToqReq` STRING,
    `TPMSAutoLoctnCm` STRING,
    `TPMSF` STRING,
    `TPMSIdficnLrnCm` STRING,
    `TPMSTirePrsLowIO` STRING,
    `TrShftLvrPos` STRING,
    `TrShftLvrPosPPC` STRING,
    `TrShftLvrPosV` STRING,
    `TWVFaultInfo` STRING,
    `VCMSRationalFaultInfo` STRING,
    `VCUSecyWrnngInfo` STRING,
    `VehCrshTyp` STRING,
    `VehDrvngMd` STRING,
    `VehDrvngMdV` STRING,
    `VehDynYawRate` STRING,
    `VehDynYawRateV` STRING,
    `VehElecRng` STRING,
    `VehElecRngDspCmd` STRING,
    `VehElecRngV` STRING,
    `VehEnrgRdyLvl` STRING,
    `VehEnrgRdyLvlV` STRING,
    `VehHzrdMdSts` STRING,
    `VehLckngSta` STRING,
    `VehLdShedLvl` STRING,
    `VehOdo` STRING,
    `VehOdoV` STRING,
    `VehSdslSts` STRING,
    `VehSideLghtSts` STRING,
    `VehSideLghtSts_l` STRING,
    `VehSideLghtSts_l_DCMFL_LIN` STRING,
    `VehSpdAvg` STRING,
    `VehSpdAvgAlvRC` STRING,
    `VehSpdAvgDrvn` STRING,
    `VehSpdAvgDrvnV` STRING,
    `VehSpdAvgNonDrvn` STRING,
    `VehSpdAvgNonDrvnV` STRING,
    `VehSpdAvgV` STRING,
    `VehSpdDpdntLvlCtrlSts` STRING,
    `VSELatAcc` STRING,
    `VSELatAccV` STRING,
    `VSELongtAcc` STRING,
    `VSELongtAccV` STRING,
    `VSEMd` STRING,
    `VSESts` STRING,
    `VSESysA` STRING,
    `wake_network_AMR` STRING,
    `wake_network_BPEPS` STRING,
    `wake_network_BPEPS_UB` STRING,
    `wake_network_MSM` STRING,
    `wake_network_MSM_Psng` STRING,
    `wake_network_PLCM` STRING,
    `WCDSInClntTemp` STRING,
    `WCondOutRefTem` STRING,
    `WhlBrkPrsSts` STRING,
    `WhlGndVelLDrvn` STRING,
    `WhlGndVelLDrvnV` STRING,
    `WhlGndVelLNonDrvn` STRING,
    `WhlGndVelLNonDrvnV` STRING,
    `WhlGndVelRDrvn` STRING,
    `WhlGndVelRDrvnV` STRING,
    `WhlGndVelRNonDrvn` STRING,
    `WhlGndVelRNonDrvnV` STRING,
    `WLC_NKI_PTEXTDCAN` STRING,
    `WLC_NOI_PTEXTDCAN` STRING,
    `WLC_NWI_PTEXTDCAN` STRING,
    `WLCAvlbly` STRING,
    `WLCHVILSts` STRING,
    `WLCHVILStsV` STRING,
    `WlcmLghtReq` STRING,
    `WlcmMdTrigHvacReq` STRING,
    `WndsrnHmdty` STRING,
    `WPTCMngmntInfo` STRING,
    `WrlsChrgrActuOtptCrnt` STRING,
    `WrlsChrgrAuxFnDetResult` STRING,
    `WrlsChrgrAuxFnDetResultV` STRING,
    `WrlsChrgrIdHiRng` STRING,
    `WrlsChrgrIdV` STRING,
    `WrlsChrgrSts` STRING,
    `WrlsChrgrWifiSts` STRING,
    `WrlsChrgrWkupA` STRING,
    `ZeroGrvySwtEntReq_l` STRING,
    `ZeroGrvySwtExitReq_l` STRING,
    `ACSyncDspCmd` STRING,
    `BMSChrgBufV` STRING,
    `BMSChrgPeakPwrV` STRING,
    `BMSDschrgCrntLmtV` STRING,
    `BMSDschrgCtnsPwrLmtV` STRING,
    `BMSDschrgPeakPwr` STRING,
    `BMSDschrgPeakPwrV` STRING,
    `BMSOnbdChrgngMd` STRING,
    `BMSStaOfHealthResi` STRING,
    `PTCOtptTem` STRING,
    `RWSActuDrvngMd` STRING,
    `VehRefSpd` STRING,
    `RWSActuRdWhlAng` STRING,
    `CrabWalkActvResp` STRING,
    `LVBMEstdCapct` STRING,
    `IBSAPASysFltSts` STRING,
    `HeatPmpOpMd` STRING,
    `EACSpd` STRING,
    `HeatPmpOpSts` STRING,
    `IWtrPTCSts` STRING,
    `IEACSts` STRING,
    `AmbtTemFaltSts` STRING,
    `ACEvapoTemFaltSts_Rr` STRING,
    `ACFcVentTemFaltSts_FrtLft` STRING,
    `ACFcVentTemFaltSts_FrtRt` STRING,
    `ACFtVentTemFaltSts_FrtLft` STRING,
    `ACFtVentTemFaltSts_FrtRt` STRING,
    `ACFtVentTemFaltSts_RrLft` STRING,
    `ACFcVentTemFaltSts_RrLft` STRING,
    `ChlrInltClntTemFaltSts` STRING,
    `ChlrOtltRefgTemFaltSts` STRING,
    `EDUInltClntTemFaltSts` STRING,
    `ITMSESSClntTemFaltSts` STRING,
    `IntkComprRefgTemFaltSts` STRING,
    `OtltComprRefgTemFaltSts` STRING,
    `WtrPTCOutlClntTemFaltSts` STRING,
    `WtrPTCMidClntTemFaltSts` STRING,
    `ACFrtInCarTemV` STRING,
    `ITMSAGS1FltInfo` STRING,
    `ITMSAGS2FltInfo` STRING,
    `ITMSFanFltInfo` STRING,
    `ACRrBlwrFaltSts` STRING,
    `ITMSEDSPumpFltInfo` STRING,
    `ITMSHtrPumpFltInfo` STRING,
    `ThrVlvFaultInfo` STRING,
    `IMuVlvFaultInfo` STRING,
    `ThrVlvFaultInfo_REV` STRING,
    `IMuVlvFaultInfo_REV` STRING,
    `ChlrEXVFaltSts` STRING,
    `EvaprEXVFaltSts` STRING,
    `ITMSTraceMdEnhancecoolMngmtInfo` STRING,
    `RefLeakFltInfo` STRING,
    `ACMdMotFaltSts_FrtLftMd` STRING,
    `ACMdMotFaltSts_FrtRr` STRING,
    `ACMdMotFaltSts_FrtRtMd` STRING,
    `ACMixAirMotFaltSts_FrtLft` STRING,
    `ACMixAirMotFaltSts_FrtRt` STRING,
    `ACMixAirMotFaltSts_RrLft` STRING,
    `ACRccMotFaltSts` STRING,
    `ACFreshMotFaltSts` STRING,
    `ACHiSideFludPrsFaltSts` STRING,
    `EACCrntSnsrFlt` STRING,
    `EACVolSnsrFlt` STRING,
    `ITMSAThrmlShtdwnFlt` STRING,
    `ITMSAThrmlWarnFlt` STRING,
    `RrHVACSVFaltSts` STRING,
    `EvaprEXVActPosn` STRING,
    `WtrPTCInltClntTem` STRING,
    `EDUInltClntTem` STRING,
    `ChlrInltClntTem` STRING,
    `WtrPTCSts` STRING,
    `EACSts` STRING,
    `VSELongtAcc_SDM` STRING,
    `ITMSESSClntTem` STRING,

    `AccConsmpAftChrg` STRING,
    `ACOnOffReq` STRING,
    `AltngChrgCrntDspCmd` STRING,
    `AMPASndWaveSelSts` STRING,
    `BatSOFVol1Sts` STRING,
    `BatTemSts` STRING,
    `BMSBatteryLifeInfo1_DA00` STRING,
    `BMSBatteryLifeInfo2_DA00` STRING,
    `BMSCellAlrdyBalcTime_DA02` STRING,
    `BMSCellBalcIdx_DA02` STRING,
    `BMSCellMinTemIndx` STRING,
    `BMSCellRmngBalcTime_DA02` STRING,
    `BMSChrgCtnsPwrLmt` STRING,
    `BMSDircPstvRelaySts` STRING,
    `BMSDircPstvRelayStsV` STRING,
    `BMSDsChrgCtrlDspCmd` STRING,
    `BMSFltIndInfo_DA02` STRING,
    `BMSOfbdChrgRelayStsV` STRING,
    `BMSPTIsolationLevel` STRING,
    `BMSRptBatCodeAsc1` STRING,
    `BMSRptBatCodeAsc2` STRING,
    `BMSRptBatCodeAsc3` STRING,
    `BMSRptBatCodeAsc4` STRING,
    `BMSRptBatCodeAsc5` STRING,
    `BMSRptBatCodeAsc6` STRING,
    `BMSRptBatCodeAsc7` STRING,
    `BMSRptBatCodeIndx` STRING,
    `BPEPSPwrSts` STRING,
    `CCUPwrSts` STRING,
    `CentrConsoleLampSts` STRING,
    `ChrgngRmnngTimeV` STRING,
    `ChrgTrgtSOCDspCmd` STRING,
    `ChrgTrgtSOCVal` STRING,
    `CMSPwrSts` STRING,
    `CurSenInfo_DA02` STRING,
    `DKMMPwrSts` STRING,
    `DLPCMPwrSts` STRING,
    `DLPLghtThemeMdBatSts` STRING,
    `DschrgTrgtSOCVal` STRING,
    `DSSADBkupPwrSts` STRING,
    `DSSADPwrSts` STRING,
    `DTCInfomationECM` STRING,
    `DTCInfomationESS` STRING,
    `DTCInfomationLVBM` STRING,
    `DTCInfomationRZCU` STRING,
    `DTCInfomationSAC` STRING,
    `DTCInfomationTC` STRING,
    `EASCCalibSts_l` STRING,
    `EASCLINRespErr_l` STRING,
    `EASCPermanentErr_l` STRING,
    `EASCTemporaryErr_l` STRING,
    `EBSAcumtdBatChrg_l` STRING,
    `EBSAcumtdBatChrg_R_l` STRING,
    `EBSAcumtdBatDschrg_l` STRING,
    `EBSAcumtdBatDschrg_R_l` STRING,
    `EBSBatSOFVol1_l` STRING,
    `EBSBatSOFVol1Sts_R_l` STRING,
    `EBSBatSOFVol2_l` STRING,
    `EBSBatTem_l` STRING,
    `EBSBatTem_R_l` STRING,
    `EBSCrntSts_l` STRING,
    `EBSCrntSts_R_l` STRING,
    `EBSErInCalData_R_l` STRING,
    `EBSErInECUIdficn_R_l` STRING,
    `EBSQucntCrnt_l` STRING,
    `EBSQucntCrnt_R_l` STRING,
    `EBSRespEr_l` STRING,
    `EBSRespEr_R_l` STRING,
    `EBSSOHOfCrsn_l` STRING,
    `EBSSOHOfCrsnSts_R_l` STRING,
    `EBSSOHOfLAM_R_l` STRING,
    `EBSSOHOfLAMSts_R_l` STRING,
    `EBSSOHOfSlphtnSts_R_l` STRING,
    `EnRunABkup` STRING,
    `EnSpdBkup` STRING,
    `EPBSwStsV` STRING,
    `EPTInfoDsp` STRING,
    `EPTSecyAxleTrInptShaftMaxAvlblToq` STRING,
    `EPTSecyAxleTrInptShaftMaxAvlblToqV` STRING,
    `EPTTrInptShaftToqV` STRING,
    `ESCLPwrSts` STRING,
    `ESSPwrSts` STRING,
    `ETCMPwrSts` STRING,
    `FDR4DPwrSts` STRING,
    `FDRPwrSts` STRING,
    `FICMV2XEnEnbReq` STRING,
    `FLiDARPwrSts` STRING,
    `FLSeatMsagMdSts` STRING,
    `FrgrLblDspCmd_l` STRING,
    `FRSeatMsagMdSts_l` STRING,
    `FrtSDAPwrSts` STRING,
    `IAMPwrSts` STRING,
    `IPDBkupPwrSts` STRING,
    `IPDPwrSts` STRING,
    `IPKSetVehEleclRngAlg` STRING,
    `IPSPwrSts` STRING,
    `ITMSEnbWPTC` STRING,
    `LHPODRPwrSts` STRING,
    `LHSWPwrSts` STRING,
    `LScrnLftrStsInd_l` STRING,
    `LVBM_NKI_PTEXTDCAN` STRING,
    `LVBM_NOI_PTEXTDCAN` STRING,
    `LVBM_NWI_PTEXTDCAN` STRING,
    `LVBMAcumtdBatChrg` STRING,
    `LVBMAcumtdBatChrgV` STRING,
    `LVBMAcumtdBatDischrg` STRING,
    `LVBMAcumtdBatDischrgV` STRING,
    `LVBMCell1Vol` STRING,
    `LVBMCell1VolV` STRING,
    `LVBMCell2Vol` STRING,
    `LVBMCell2VolV` STRING,
    `LVBMCell3Vol` STRING,
    `LVBMCell3VolV` STRING,
    `LVBMCell4Vol` STRING,
    `LVBMCell4VolV` STRING,
    `LVBMChrgCrntLmt` STRING,
    `LVBMChrgCrntLmtV` STRING,
    `LVBMChrgVolDynCalReq` STRING,
    `LVBMChrgVolReq` STRING,
    `LVBMCrntSts` STRING,
    `LVBMDschrgCrntLmt` STRING,
    `LVBMDschrgCrntLmtV` STRING,
    `LVBMIntnlRstcPrstTem` STRING,
    `LVBMMaxCellTem` STRING,
    `LVBMMaxCellTemV` STRING,
    `LVBMMinCellTem` STRING,
    `LVBMMinCellTemV` STRING,
    `LVBMTotVolSts` STRING,
    `MapLampFLSts_l` STRING,
    `MinuteOfHour` STRING,
    `NFCAMPwrSts` STRING,
    `NFCSMPwrSts` STRING,
    `PMAPwrSts` STRING,
    `PTCActuCrnt` STRING,
    `PTCActuVol` STRING,
    `PTCFltLvl` STRING,
    `PTCHVLckSts` STRING,
    `PTCInptTem` STRING,
    `PTCOverVol` STRING,
    `PTCSts` STRING,
    `PTCUnderVol` STRING,
    `ReserChrgSts` STRING,
    `ReserOnbdChrgCtrlReq` STRING,
    `RHPODRPwrSts` STRING,
    `RIPDPwrSts` STRING,
    `RLSSPwrSts` STRING,
    `RmtChrgTrgtSOCReq` STRING,
    `RmtReserCtrlReq` STRING,
    `RmtReserSpHour` STRING,
    `RmtReserSpMinute` STRING,
    `RmtReserStHour` STRING,
    `RmtReserStMinute` STRING,
    `RrMPWCPwrSts` STRING,
    `RrSDAPwrSts` STRING,
    `RrSwPwrPwrSts` STRING,
    `SASPwrSts` STRING,
    `SDMBkupPwrSts` STRING,
    `SDMPwrSts` STRING,
    `SysBPM` STRING,
    `SysBPMEnbd` STRING,
    `SysVolMd` STRING,
    `TCPwrSts` STRING,
    `ThrdRowUSBPwrSts` STRING,
    `TMActuToqV` STRING,
    `TMInvtrCrntV` STRING,
    `TotalRegenEnrg` STRING,
    `TotlConsmpAftChrg` STRING,
    `TrOtptRotlStsV` STRING,
    `VDCCtrlStat` STRING,
    `VehActuElecCsump` STRING,
    `VehActuElecCsumpV` STRING,
    `VehActuElecRng` STRING,
    `VehActuElecRngV` STRING,
    `ZXDBkupPwrSts` STRING,
    `ZXDPwrSts` STRING,

    `dt` STRING,
    gwt bigint ,
    dpt bigint,
    `SecRowRSeatMsagPrtSts_l` STRING,
    kafka_receive_time bigint,
    sink_paimon_time bigint,
    PRIMARY KEY (`rct`, `vin`, `dt`) NOT ENFORCED
  ) PARTITIONED BY (`dt`)
WITH
  (
    'bucket' = '32', -- 根据数据量按照单bucket 2GB 左右进行配置，原始数据在列存中压缩率很高，单天约 1TB 文件产生
    'merge-engine' = 'partial-update', -- 相同主键数据自动合并不为 Null 的列
    'metadata.stats-mode' = 'none',
    'fields.vin.stats-mode' = 'truncate(20)', --主键统计信息，仅做统计用，超过20截取。
--    'fields.ct.stats-mode' = 'truncate(16)',
--    'fields.st.stats-mode' = 'truncate(16)',
--    'fields.at.stats-mode' = 'truncate(16)',
--    'fields.uuid.stats-mode' = 'truncate(16)',
--    'fields.col.stats-mode' = 'truncate(16)',
    'fields.rct.stats-mode' = 'truncate(20)',
 --   'fields.r.stats-mode' = 'truncate(16)',
    'read.batch-size' = '32', -- 列数角度，减少读取时占用的内存，这里主要用于 compact
    -- 'target-file-size' = '512mb', -- 目标单文件大小，减少小文件数量
    'manifest.target-file-size' = '8mb', -- Manifest 文件大小，减少 IO
    'manifest.full-compaction-threshold-size' = '16mb', -- 合并小文件，减少 Manifest 数量
    'manifest.merge-min-count' = '20', -- 避免频繁合并 Manifest
    'snapshot.expire.execution-mode' = 'async', -- 数据量较大，改为异步删除过期文件的模式，不会阻塞上游

    -- 'orc.write.batch-size' = '512', -- 列数较多，减少写入占用的内存
    'target-file-size' = '128mb', -- 目标单文件大小，减少小文件数量
    'file.format' = 'parquet',  --  文件格式改成 parquet，减少 TOS 存储量
    'file.compression' = 'zstd',  -- 文件压缩方式改成 zstd
    'parquet.compression.codec.zstd.level' = '3', -- zstd 压缩级别调整为 3， 过高消耗较高 cpu
    'parquet.bloom.filter.enabled#vin' = 'true',  -- 开启Parquet的 Bloom filter (用于高频查询的字段)
    'parquet.bloom.filter.enabled#rct' = 'true',
    'deletion-vectors.enabled' = 'true',  --  开启 deletion vector， 以支持 Starrocks 并发读提高查询性能；
    'file-index.bloom-filter.columns' = 'vin', -- 开启Paimon的 file index，用于提供查询性能，支持配置多个字段,用逗号隔开
    'bucket-key' = 'vin',  -- 新增 bucket key 加速查询时数据过滤，选择的字段数据分布要均匀
    'write-buffer-spillable' = 'true'  -- 用于 compaction 排序时溢写到磁盘, 防止数据量过大时 OOM
  );

