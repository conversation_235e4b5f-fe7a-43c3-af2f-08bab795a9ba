import re
import os
from pyflink.table import EnvironmentSettings, TableEnvironment

def parse_ddl_columns(ddl_file_path):
    """
    从 Paimon DDL 文件中解析列名。
    """
    with open(ddl_file_path, 'r') as f:
        ddl_content = f.read()

    # 找到第一个 '(' 和与之匹配的 ')'
    start_index = ddl_content.find('(')
    if start_index == -1:
        raise ValueError("DDL中未找到'('，无法解析列")

    balance = 1
    end_index = -1
    for i in range(start_index + 1, len(ddl_content)):
        if ddl_content[i] == '(':
            balance += 1
        elif ddl_content[i] == ')':
            balance -= 1
        if balance == 0:
            end_index = i
            break

    if end_index == -1:
        raise ValueError("DDL中未找到匹配的')'，无法解析列")

    columns_str = ddl_content[start_index + 1:end_index]
    
    # 使用正则表达式提取列名
    columns = re.findall(r'`(\w+)`', columns_str)
    return columns

def generate_comparison_sql(table1, table2, columns, pk_column):
    """
    生成用于比较两个表的 Flink SQL。
    """
    if pk_column not in columns:
        raise ValueError(f"主键 '{pk_column}' 不在列列表中。")

    select_clauses = []
    select_clauses.append(f"t1.`{pk_column}` AS {pk_column}_t1")
    select_clauses.append(f"t2.`{pk_column}` AS {pk_column}_t2")

    for col in columns:
        if col != pk_column:
            # 注意：对于浮点数或复杂类型的比较，可能需要调整比较逻辑
            select_clauses.append(
                f"CASE WHEN t1.`{col}` IS NOT DISTINCT FROM t2.`{col}` THEN 'MATCH' ELSE 'MISMATCH' END AS {col}_match"
            )
            select_clauses.append(f"t1.`{col}` AS {col}_t1")
            select_clauses.append(f"t2.`{col}` AS {col}_t2")

    sql = f"""\
    SELECT
        {',\n        '.join(select_clauses)}
    FROM {table1} AS t1
    FULL OUTER JOIN {table2} AS t2 ON t1.`{pk_column}` = t2.`{pk_column}`
    LIMIT 1000
    """
    return sql

def main():
    # --- 配置项 --- #
    # 请将这里的表名替换为您的0.6和0.8版本的Paimon表名
    # 例如: 'paimon_catalog.default.my_table_v06'
    table_v06 = 'paimon_catalog.imedw.dwd_prd_ddc_data_periodcanlin_p12l_format_s_di_v06' 
    table_v08 = 'paimon_catalog.imedw.dwd_prd_ddc_data_periodcanlin_p12l_format_s_di_v08'

    # 用于JOIN的主键
    primary_key = 'vin'

    # DDL文件路径
    ddl_file = '/Users/<USER>/Desktop/tmp/20250623/paimon.sql'

    # Flink SQL Gateway 的地址和端口
    sql_gateway_host = 'localhost'
    sql_gateway_port = 8083
    # --- 配置结束 --- #

    if not os.path.exists(ddl_file):
        print(f"错误: DDL文件 '{ddl_file}' 不存在.")
        return

    try:
        print("1. 正在解析DDL文件以提取列...")
        columns = parse_ddl_columns(ddl_file)
        print(f"   成功提取 {len(columns)} 个列.")

        print("2. 正在生成Flink对比SQL查询...")
        comparison_sql = generate_comparison_sql(table_v06, table_v08, columns, primary_key)
        print("   生成的SQL查询:")
        print("-" * 40)
        print(comparison_sql)
        print("-" * 40)

        print("3. 正在连接到Flink SQL Gateway并执行查询...")
        env_settings = EnvironmentSettings.new_instance().in_streaming_mode().build()
        t_env = TableEnvironment.create(env_settings)
        
        # 配置SQL Gateway
        t_env.get_config().get_configuration().set_string("sql-gateway.endpoint.address", sql_gateway_host)
        t_env.get_config().get_configuration().set_integer("sql-gateway.endpoint.port", sql_gateway_port)

        # 执行查询
        # 注意: pyflink 执行SQL Gateway查询目前需要更复杂的设置，
        # 这里我们直接打印SQL，并展示如何处理结果（假设已获取结果）。
        # 您可以将生成的SQL复制到Flink SQL客户端中执行。
        print("\n*** 请将以上SQL复制到您的Flink SQL客户端或通过SQL Gateway API执行 ***\n")

        # 假设您已经执行了SQL并获取了结果，以下是处理结果的示例逻辑
        # MOCK_RESULTS 是一个模拟的结果集
        # 在实际使用中，您需要用真实结果替换它
        MOCK_RESULTS = [
            # (vin_t1, vin_t2, col1_match, col1_t1, col1_t2, ...)
            ('vin1', 'vin1', 'MATCH', 'A', 'A', 'MISMATCH', 1, 2), 
            ('vin2', None, 'MISMATCH', 'B', None, 'MISMATCH', 3, None),
            (None, 'vin3', 'MISMATCH', None, 'C', 'MISMATCH', None, 4)
        ]
        
        print("4. 分析对比结果...")
        mismatched_fields = []
        for row in MOCK_RESULTS: # 替换为您的真实结果集
            row_dict = {}
            # 将行数据转换为字典以便于访问
            # 这个转换逻辑需要根据您的真实列来调整
            # 这里仅为示例
            row_dict['vin_t1'] = row[0]
            row_dict['vin_t2'] = row[1]
            i = 2
            for col in columns:
                if col != primary_key:
                    row_dict[f'{col}_match'] = row[i]
                    row_dict[f'{col}_t1'] = row[i+1]
                    row_dict[f'{col}_t2'] = row[i+2]
                    i += 3

            pk_val = row_dict.get('vin_t1') or row_dict.get('vin_t2')
            for col in columns:
                if col != primary_key:
                    match_status = row_dict.get(f'{col}_match')
                    if match_status == 'MISMATCH':
                        mismatched_fields.append({
                            'primary_key': pk_val,
                            'field': col,
                            'value_table1': row_dict.get(f'{col}_t1'),
                            'value_table2': row_dict.get(f'{col}_t2')
                        })

        if mismatched_fields:
            print("\n发现未对齐的字段:")
            for item in mismatched_fields:
                print(
                    f"  - 主键: {item['primary_key']}, "
                    f"字段: {item['field']}, "
                    f"表1值: {item['value_table1']}, "
                    f"表2值: {item['value_table2']}"
                )
        else:
            print("\n在抽样的1000条记录中，所有字段均完全对齐。")

    except Exception as e:
        print(f"\n发生错误: {e}")

if __name__ == '__main__':
    main()