#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Paimon 表对比 SQL 生成器
用于生成 Flink SQL 来对比两个不同版本的 Paimon 表
"""

import re
import sys
from typing import List, Tuple


class PaimonCompareGenerator:
    def __init__(self, ddl_file_path: str):
        self.ddl_file_path = ddl_file_path
        self.catalog_v06 = "paimon_catalog_v06"
        self.catalog_v08 = "paimon_catalog_v08"
        self.database_name = "imedw"
        self.table_name = "dwd_prd_ddc_data_periodcanlin_p12l_format_s_di"

        # 定义主键字段（根据 DDL 分析，这些可能是主键字段）
        self.primary_key_fields = ["vin", "ct", "st", "at", "uuid"]

        self.fields = []
        self.non_pk_fields = []
        
    def parse_ddl_file(self) -> List[str]:
        """解析 DDL 文件，提取所有字段名"""
        fields = []
        
        try:
            with open(self.ddl_file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
            # 使用正则表达式提取字段定义
            # 匹配类似 `field_name` STRING, 的模式
            field_pattern = r'`([^`]+)`\s+STRING'
            matches = re.findall(field_pattern, content)
            
            fields = [match for match in matches if match]
            
            print(f"从 DDL 文件中解析出 {len(fields)} 个字段")
            
        except FileNotFoundError:
            print(f"错误：找不到文件 {self.ddl_file_path}")
            sys.exit(1)
        except Exception as e:
            print(f"解析 DDL 文件时出错：{e}")
            sys.exit(1)
            
        return fields
    
    def prepare_fields(self):
        """准备字段列表"""
        self.fields = self.parse_ddl_file()
        
        # 分离主键字段和非主键字段
        self.non_pk_fields = [field for field in self.fields 
                             if field not in self.primary_key_fields]
        
        print(f"主键字段：{self.primary_key_fields}")
        print(f"非主键字段数量：{len(self.non_pk_fields)}")
    
    def generate_join_condition(self) -> str:
        """生成 JOIN 条件"""
        conditions = []
        for pk_field in self.primary_key_fields:
            conditions.append(f"t1.`{pk_field}` = t2.`{pk_field}`")
        return " AND ".join(conditions)
    
    def generate_where_condition(self) -> str:
        """生成 WHERE 条件，用于找出字段值不同的记录"""
        conditions = []
        
        # 对每个非主键字段生成不等条件
        for field in self.non_pk_fields:
            # 处理 NULL 值的比较
            condition = f"""(
                (t1.`{field}` IS NULL AND t2.`{field}` IS NOT NULL) OR
                (t1.`{field}` IS NOT NULL AND t2.`{field}` IS NULL) OR
                (t1.`{field}` IS NOT NULL AND t2.`{field}` IS NOT NULL AND t1.`{field}` != t2.`{field}`)
            )"""
            conditions.append(condition)
        
        return " OR ".join(conditions)
    
    def generate_simple_comparison_sql(self) -> str:
        """生成简化的对比 SQL，只显示主键和差异统计，输出到 Print Sink"""

        table_v06 = f"`{self.catalog_v06}`.`{self.database_name}`.`{self.table_name}`"
        table_v08 = f"`{self.catalog_v08}`.`{self.database_name}`.`{self.table_name}`"

        join_condition = self.generate_join_condition()
        where_condition = self.generate_where_condition()

        # 生成主键字段选择
        pk_fields_ddl = []
        pk_select = []
        for pk_field in self.primary_key_fields:
            pk_fields_ddl.append(f"    `{pk_field}` STRING")
            pk_select.append(f"    t1.`{pk_field}`")

        # 生成差异字段统计
        diff_count_conditions = []
        for field in self.non_pk_fields[:100]:  # 限制前100个字段避免SQL过长
            condition = f"""
        CASE WHEN (t1.`{field}` IS NULL AND t2.`{field}` IS NOT NULL) OR
                  (t1.`{field}` IS NOT NULL AND t2.`{field}` IS NULL) OR
                  (t1.`{field}` IS NOT NULL AND t2.`{field}` IS NOT NULL AND t1.`{field}` != t2.`{field}`)
             THEN 1 ELSE 0 END"""
            diff_count_conditions.append(condition)

        diff_count_sql = " + ".join(diff_count_conditions)

        sql = f"""-- Paimon 表简化对比 SQL (0.6 vs 0.8 版本) - 输出到 Print Sink

-- 1. 创建 Print Sink 表
CREATE TABLE print_simple_comparison (
{chr(10).join(pk_fields_ddl)},
    diff_field_count INT,
    note STRING,
    comparison_time TIMESTAMP(3)
) WITH (
    'connector' = 'print',
    'print-identifier' = 'paimon-simple-comparison'
);

-- 2. 插入简化对比结果到 Print Sink
INSERT INTO print_simple_comparison
SELECT
{chr(10).join(pk_select)},
    ({diff_count_sql}) as diff_field_count,
    'Check detailed comparison for specific field differences' as note,
    CURRENT_TIMESTAMP as comparison_time
FROM {table_v06} t1
INNER JOIN {table_v08} t2
ON {join_condition}
WHERE {where_condition}
LIMIT 1000;"""

        return sql

    def generate_detailed_field_comparison_sql(self) -> str:
        """生成详细的字段对比 SQL，显示具体的字段差异，输出到 Print Sink"""

        table_v06 = f"`{self.catalog_v06}`.`{self.database_name}`.`{self.table_name}`"
        table_v08 = f"`{self.catalog_v08}`.`{self.database_name}`.`{self.table_name}`"

        join_condition = self.generate_join_condition()

        # 生成主键字段选择和DDL
        pk_fields_ddl = []
        pk_select = []
        for pk_field in self.primary_key_fields:
            pk_fields_ddl.append(f"    `{pk_field}` STRING")
            pk_select.append(f"    t1.`{pk_field}`")

        # 生成字段对比，使用 CONCAT_WS 来组合所有差异
        field_comparisons = []
        for field in self.non_pk_fields[:50]:  # 限制字段数量
            comparison = f"""
        CASE WHEN (t1.`{field}` IS NULL AND t2.`{field}` IS NOT NULL) OR
                  (t1.`{field}` IS NOT NULL AND t2.`{field}` IS NULL) OR
                  (t1.`{field}` IS NOT NULL AND t2.`{field}` IS NOT NULL AND t1.`{field}` != t2.`{field}`)
             THEN CONCAT('{field}:[v0.6]=', COALESCE(CAST(t1.`{field}` AS STRING), 'NULL'),
                        ',[v0.8]=', COALESCE(CAST(t2.`{field}` AS STRING), 'NULL'))
             ELSE NULL END"""
            field_comparisons.append(comparison)

        concat_fields = ",\n        ".join(field_comparisons)

        sql = f"""-- Paimon 表详细字段对比 SQL (0.6 vs 0.8 版本) - 输出到 Print Sink

-- 1. 创建 Print Sink 表
CREATE TABLE print_detailed_comparison (
{chr(10).join(pk_fields_ddl)},
    field_differences STRING,
    comparison_time TIMESTAMP(3)
) WITH (
    'connector' = 'print',
    'print-identifier' = 'paimon-detailed-comparison'
);

-- 2. 插入详细对比结果到 Print Sink
INSERT INTO print_detailed_comparison
SELECT
{chr(10).join(pk_select)},
    CONCAT_WS(' | ',
        {concat_fields}
    ) as field_differences,
    CURRENT_TIMESTAMP as comparison_time
FROM {table_v06} t1
INNER JOIN {table_v08} t2
ON {join_condition}
WHERE (
    {self.generate_where_condition()}
)
LIMIT 1000;"""

        return sql
    
    def generate_field_by_field_sql(self, field_name: str) -> str:
        """为特定字段生成对比 SQL，输出到 Print Sink"""

        table_v06 = f"`{self.catalog_v06}`.`{self.database_name}`.`{self.table_name}`"
        table_v08 = f"`{self.catalog_v08}`.`{self.database_name}`.`{self.table_name}`"

        join_condition = self.generate_join_condition()

        # 生成主键字段选择和DDL
        pk_fields_ddl = []
        pk_select = []
        for pk_field in self.primary_key_fields:
            pk_fields_ddl.append(f"    `{pk_field}` STRING")
            pk_select.append(f"    t1.`{pk_field}`")

        sql = f"""-- 特定字段 '{field_name}' 对比 SQL - 输出到 Print Sink

-- 1. 创建 Print Sink 表
CREATE TABLE print_field_comparison_{field_name} (
{chr(10).join(pk_fields_ddl)},
    v06_{field_name} STRING,
    v08_{field_name} STRING,
    comparison_result STRING,
    comparison_time TIMESTAMP(3)
) WITH (
    'connector' = 'print',
    'print-identifier' = 'paimon-field-{field_name}-comparison'
);

-- 2. 插入字段对比结果到 Print Sink
INSERT INTO print_field_comparison_{field_name}
SELECT
{chr(10).join(pk_select)},
    CAST(t1.`{field_name}` AS STRING) as v06_{field_name},
    CAST(t2.`{field_name}` AS STRING) as v08_{field_name},
    CASE
        WHEN t1.`{field_name}` IS NULL AND t2.`{field_name}` IS NOT NULL THEN 'v0.6_NULL_v0.8_NOT_NULL'
        WHEN t1.`{field_name}` IS NOT NULL AND t2.`{field_name}` IS NULL THEN 'v0.6_NOT_NULL_v0.8_NULL'
        WHEN t1.`{field_name}` != t2.`{field_name}` THEN 'VALUES_DIFFERENT'
        ELSE 'SAME'
    END as comparison_result,
    CURRENT_TIMESTAMP as comparison_time
FROM {table_v06} t1
INNER JOIN {table_v08} t2
ON {join_condition}
WHERE (t1.`{field_name}` IS NULL AND t2.`{field_name}` IS NOT NULL) OR
      (t1.`{field_name}` IS NOT NULL AND t2.`{field_name}` IS NULL) OR
      (t1.`{field_name}` IS NOT NULL AND t2.`{field_name}` IS NOT NULL AND t1.`{field_name}` != t2.`{field_name}`)
LIMIT 1000;"""

        return sql
    
    def generate_summary_sql(self) -> str:
        """生成汇总 SQL，统计差异数量，输出到 Print Sink"""

        table_v06 = f"`{self.catalog_v06}`.`{self.database_name}`.`{self.table_name}`"
        table_v08 = f"`{self.catalog_v08}`.`{self.database_name}`.`{self.table_name}`"

        join_condition = self.generate_join_condition()
        where_condition = self.generate_where_condition()

        sql = f"""-- 差异记录汇总统计 - 输出到 Print Sink

-- 1. 创建 Print Sink 表
CREATE TABLE print_diff_summary (
    total_diff_records BIGINT,
    diff_vin_count BIGINT,
    summary_time TIMESTAMP(3)
) WITH (
    'connector' = 'print',
    'print-identifier' = 'paimon-diff-summary'
);

-- 2. 插入差异统计结果到 Print Sink
INSERT INTO print_diff_summary
SELECT
    COUNT(*) as total_diff_records,
    COUNT(DISTINCT t1.`vin`) as diff_vin_count,
    CURRENT_TIMESTAMP as summary_time
FROM {table_v06} t1
INNER JOIN {table_v08} t2
ON {join_condition}
WHERE {where_condition};"""

        return sql

    def generate_catalog_setup_sql(self) -> str:
        """生成 Catalog 创建语句"""

        sql = f"""-- Paimon Catalog 设置 SQL
-- 创建两个不同版本的 Paimon Catalog

-- 1. 创建 0.6 版本的 Catalog
CREATE CATALOG {self.catalog_v06}
WITH
  (
    'type' = 'paimon',
    'warehouse' = 'tos://hs-bdp-private-prod/user/paimon/warehouse',
    'fs.s3a.connection.maximum' = '1000'
  );

-- 2. 创建 0.6 版本的 Database
CREATE DATABASE IF NOT EXISTS {self.catalog_v06}.{self.database_name};

-- 3. 创建 0.8 版本的 Catalog
CREATE CATALOG {self.catalog_v08}
WITH
  (
    'type' = 'paimon',
    'warehouse' = 'tos://hs-bdp-private-lakehouse-prod/user/paimon/warehouse',
    'fs.s3a.connection.maximum' = '1000'
  );

-- 4. 创建 0.8 版本的 Database
CREATE DATABASE IF NOT EXISTS {self.catalog_v08}.{self.database_name};

-- 注意：执行对比 SQL 之前，请先执行上述 Catalog 创建语句
"""

        return sql


def main():
    """主函数"""
    print("=== Paimon 表对比 SQL 生成器 ===")
    print()

    # DDL 文件路径
    ddl_file_path = "/Users/<USER>/Desktop/tmp/20250623/paimon.sql"

    # 创建生成器实例
    generator = PaimonCompareGenerator(ddl_file_path)

    # 解析字段
    generator.prepare_fields()
    print()

    # 生成差异统计 SQL
    print("=== 1. 差异记录统计 SQL ===")
    print()
    summary_sql = generator.generate_summary_sql()
    print(summary_sql)
    print()

    # 生成简化对比 SQL
    print("=== 2. 简化对比 SQL (显示主键和差异字段数量) ===")
    print()
    simple_sql = generator.generate_simple_comparison_sql()
    print(simple_sql)
    print()

    # 生成详细字段对比 SQL
    print("=== 3. 详细字段对比 SQL (显示具体差异内容) ===")
    print()
    detailed_sql = generator.generate_detailed_field_comparison_sql()
    print(detailed_sql)
    print()

    # 生成单个字段对比示例
    print("=== 4. 单个字段对比 SQL 示例 (以 'ABSA' 字段为例) ===")
    print()
    field_sql = generator.generate_field_by_field_sql("ABSA")
    print(field_sql)
    print()

    print("=== 使用说明 ===")
    print("1. 请根据实际情况修改表名后缀（_v06, _v08）")
    print("2. 确保两个版本的表都存在于指定的 catalog 和 database 中")
    print("3. 建议按顺序执行：")
    print("   - 先运行统计 SQL 了解差异记录总数")
    print("   - 再运行简化对比 SQL 查看差异概况")
    print("   - 最后运行详细对比 SQL 查看具体差异")
    print("4. 对于特定字段的详细分析，可以使用单个字段对比 SQL")
    print("5. 由于字段数量很多(1880个)，详细对比SQL只显示前50个字段")
    print("6. 可以修改脚本中的字段限制数量来调整显示范围")


if __name__ == "__main__":
    main()
