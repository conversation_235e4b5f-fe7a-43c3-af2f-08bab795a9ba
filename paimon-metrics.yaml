#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Set root logger level to OFF to not flood build logs
# set manually to INFO for debugging purposes

# global: jobname will be in the tags
global:
  name:
    [
        numRestarts,
        fullRestarts,
        fullRestartsRate,
        downtime,
        uptime,
        executionStatus,
        allExecutionRestartingTime,
        jobVersion,
        currentOffsetsRate,
        consumerRecordsRate,
        numberOfFailedCheckpoints,
        numberOfContinuousCheckpointFailure,
        totalNumberOfCheckpoints,
        ratioTaskLoadSkew,
      # used in dorado platform
        lastCheckpointDuration,
        numberOfHdfsRetries,
        checkpointWriteFileRate,
        checkpointWriteFileLatency,
        checkpointCloseFileLatency
    ]

# non-global: jobname will be in the metric name
non-global:
  name:
    [
      # job info
        downtime,
        uptime,
        numRestarts,
        executionStatus,
        allExecutionRestartingTime,
        jobVersion,
      # used in dorado platform
        fullRestarts,
        numRunningJobs,
      # TaskManager / Slot
        numRegisteredTaskManagers,
        taskSlotsTotal,
        taskSlotsAvailable,
      # tasks
        numberOfTasks,
      # Memory
        Used,
        Committed,
        Max,
        Total,
      # GC
        Count,
        Time,
      # Task In/Out Queue
        inputQueueLength,
        outputQueueLength,
      # Task In/Out Pool Usage
        inPoolUsage,
        outPoolUsage,
      # Task InputChannels number
        numberOfInputChannels,
      # Task work status
        idleTimeMsPerSecond,
        backPressuredTimeMsPerSecond,
        busyTimeMsPerSecond,
      # In/Out Record Number
      # used in dorado platform
        numRecordsInPerSecond,
        numRecordsOutPerSecond,
      # late Records Dropped
        numLateRecordsDropped,
      # latency
        latency,
      # lag
        pendingRecords,
        currentFetchEventTimeLag,
        currentEmitEventTimeLag,
      # sink
        numRecordsIn,
        numBytesIn,
        numRecordsOut,
        numBytesOut,
        numRecordsSend,
        numBytesSend,
        numBytesOutPerSecond,
        numBytesSendPerSecond,
        numRecordsSendPerSecond,
      # kafka offset
        currentOffsets,
        commit-rate,
      # kafka connector verison
        kafkaConnectorVersion,
      # rocketmq connector verison
        rocketmqConnectorVersion,
      # Network Memory
        TotalMemorySegments,
        AllocatedMemorySegments,
        UsedMemorySegments,
        AvailableMemorySegments,
        TotalMemory,
        AllocatedMemory,
        UsedMemory,
        AvailableMemory,
      # CPU
        Load,
        Cores,
      # Checkpoints
      # used in dorado platform
        lastCheckpointDuration,
      # used in dorado platform
        numberOfFailedCheckpoints,
        numberOfContinuousCheckpointFailure,
        numberOfSucceedSavepoints,
        totalNumberOfCheckpoints,
        lastCheckpointSize,
      # paimon scan
        lastScanDuration,
        scanDuration,
        lastScannedManifests,
        lastSkippedByPartitionAndStats,
        lastSkippedByBucketAndLevelFilter,
        lastSkippedByWholeBucketFilesFilter,
        lastScanSkippedTableFiles,
        lastScanResultedTableFiles,
      # paimon commit
        lastCommitDuration,
        commitDuration,
        lastCommitAttempts,
        lastTableFilesAdded,
        lastTableFilesDeleted,
        lastTableFilesAppended,
        lastTableFilesCommitCompacted,
        lastChangelogFilesAppended,
        lastChangelogFileCommitCompacted,
        lastGeneratedSnapshots,
        lastDeltaRecordsAppended,
        lastChangelogRecordsAppended,
        lastDeltaRecordsCommitCompacted,
        lastChangelogRecordsCommitCompacted,
        lastPartitionsWritten,
        lastBucketsWritten,
      # paimon write buffer
        bufferPreemptCount,
        usedWriteBufferSizeByte,
        totalWriteBufferSizeByte,
      # paimon compaction
        maxLevel0FileCount,
        avgLevel0FileCount,
        compactionThreadBusy
    ]
