import os

file_path = '/Users/<USER>/Desktop/tmp/ep33.sql'
prefix = 'flink_taskmanager_job_task_operator_paimon_table_commit_'

# Check if the file exists
if not os.path.exists(file_path):
    print(f"Error: File not found at {file_path}")
    exit(1)

try:
    with open(file_path, 'r') as f:
        lines = f.readlines()

    modified_lines = [prefix + line for line in lines]

    with open(file_path, 'w') as f:
        f.writelines(modified_lines)

    print(f"Successfully added prefix to all lines in {file_path}")

except Exception as e:
    print(f"An error occurred: {e}")
    exit(1)